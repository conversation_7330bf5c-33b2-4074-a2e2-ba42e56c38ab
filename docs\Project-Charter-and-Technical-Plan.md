# Подробный план разработки системы учета рабочего времени для строительства

## 1. ТЕХНИЧЕСКАЯ АРХИТЕКТУРА

### Рекомендуемый технологический стек:

**Backend:**
- **Python FastAPI** (вместо aiogram) - более подходящий для REST API и веб-сервисов
- **PostgreSQL** - основная БД
- **Redis** - для кэширования и сессий
- **Celery** - для фоновых задач (генерация отчетов)

**Frontend:**
- **Flutter** - мобильное приложение (iOS/Android)
- **Vue.js + Nuxt.js** - веб-панель директора
- **Bootstrap/Tailwind CSS** - быстрое прототипирование UI

**AI для обработки текста:**
- **OpenAI GPT-4** (платный, $0.03/1K токенов) - высокое качество парсинга
- **Альтернатива:** Yandex GPT API (дешевле для РФ)
- **Бесплатная альтернатива:** Ollama + Llama 3.1 (локально)

**Инфраструктура:**
- **Docker** - контейнеризация
- **Nginx** - прокси-сервер
- **PostgreSQL** - основная БД
- **Minio/AWS S3** - файловое хранилище

## 2. АРХИТЕКТУРА БАЗЫ ДАННЫХ

### Основные таблицы:

```sql
-- Компании
companies (id, name, created_at, settings)

-- Пользователи
users (id, email, phone, name, role, company_id, created_at)

-- Строительные объекты
projects (id, name, address, company_id, status, created_at)

-- Записи о работах
work_entries (id, user_id, project_id, date, description, 
              work_type, quantity, unit, hours, raw_input, 
              created_at, updated_at)

-- Табели для подписания
timesheets (id, user_id, project_id, period_start, period_end,
            status, approval_link, approved_at, approved_by,
            created_at)

-- Одобрения генподрядчиков  
approvals (id, timesheet_id, contractor_name, contractor_email,
           approved_at, approval_token)
```

## 3. ДЕТАЛЬНЫЙ ПЛАН РАЗРАБОТКИ

### ЭТАП 1: ПОДГОТОВКА И ИНФРАСТРУКТУРА (1-2 недели)

#### Задача 1.1: Настройка проекта и документации
**Для AI-агента:**
```
Создай структуру проекта со следующими компонентами:
- Папка backend/ с FastAPI проектом
- Папка frontend/mobile/ с Flutter проектом  
- Папка frontend/web/ с Vue.js проектом
- Папка docs/ для документации
- docker-compose.yml для локальной разработки
- README.md с инструкциями запуска
- requirements.txt и pubspec.yaml с зависимостями

Структура документации должна включать:
- API документацию (автогенерируемую через FastAPI)
- Техническую архитектуру системы
- Диаграммы БД (используй Mermaid)
- Пользовательские сценарии (User Stories)
```

#### Задача 1.2: Создание базы данных
**Для AI-агента:**
```
Создай миграции PostgreSQL с таблицами:
1. companies - хранение данных компаний
2. users - пользователи с ролями (worker/director)  
3. projects - строительные объекты
4. work_entries - записи о выполненных работах
5. timesheets - сгенерированные табели
6. approvals - подтверждения от генподрядчиков

Добавь индексы для быстрого поиска по:
- user_id + date (для работников)
- company_id + period (для директоров)
- approval_token (для генподрядчиков)

Создай seed-данные для тестирования с примерами:
- 2 тестовые компании
- 5 рабочих и 2 директора
- 3 строительных объекта
- 20 записей о работах за последние 2 недели
```

### ЭТАП 2: BACKEND API (2-3 недели)

#### Задача 2.1: Основные эндпоинты API
**Для AI-агента:**
```
Реализуй REST API на FastAPI с эндпоинтами:

АУТЕНТИФИКАЦИЯ:
POST /api/auth/register - регистрация
POST /api/auth/login - вход  
POST /api/auth/refresh - обновление токена
GET /api/auth/me - информация о текущем пользователе

КОМПАНИИ:
POST /api/companies/ - создание компании (только директор)
GET /api/companies/my - информация о своей компании
POST /api/companies/invite - пригласить рабочего
POST /api/companies/join - присоединиться к компании

ПРОЕКТЫ:
GET /api/projects/ - список проектов пользователя
POST /api/projects/ - создать проект
PUT /api/projects/{id} - обновить проект
GET /api/projects/{id}/workers - рабочие на проекте

РАБОТЫ:
POST /api/work-entries/ - добавить запись о работе
GET /api/work-entries/ - список записей с фильтрами
PUT /api/work-entries/{id} - редактировать запись
DELETE /api/work-entries/{id} - удалить запись

Каждый эндпоинт должен:
- Проверять права доступа (JWT токены)
- Валидировать входные данные (Pydantic модели)
- Возвращать структурированные ответы
- Логировать важные действия
- Обрабатывать ошибки gracefully
```

#### Задача 2.2: AI-обработка текстового ввода
**Для AI-агента:**
```
Создай сервис для парсинга естественного языка:

Функции сервиса:
1. parse_work_input(text: str) -> WorkEntry
   - Извлекает дату из текста ("вчера", "позавчера", "15.12", "в понедельник")
   - Определяет описание работы ("шлифовал полы на 2 этаже")  
   - Извлекает объем ("80м2", "5 часов", "20 штук")
   - Определяет тип работы (часовая/сдельная/штучная)

2. Интеграция с OpenAI API:
   - Промпт для извлечения структурированных данных
   - Обработка ошибок API
   - Фолбэк на локальные правила при недоступности AI

Примеры для обработки:
- "вчера шлифовал полы на 2 этаже 80м2" 
- "сегодня работал 8 часов на монтаже"
- "во вторник установил 15 окон в доме А"
- "на прошлой неделе делал стяжку 120 квадратов"

Промпт для AI:
"Извлеки из текста дату, описание работы, количество и единицы измерения. 
Верни JSON: {date: 'YYYY-MM-DD', description: 'текст', 
quantity: число, unit: 'м2|часы|шт|кг'}"
```

#### Задача 2.3: Генерация табелей и отчетов
**Для AI-агента:**
```
Создай сервис генерации документов:

1. Генератор табелей (tuntilist):
   - Формат: Excel файл с таблицей работ
   - Поля: дата, описание, количество, тип работ
   - Шапка: название компании, имя рабочего, объект, период
   - Место для подписи внизу

2. Отчеты для директора:
   - По рабочему: все работы за период
   - По объекту: все рабочие и их работы  
   - Сводный: статистика по компании

3. Веб-страница для подписания:
   - Уникальная ссылка с токеном
   - Отображение табеля в читаемом виде
   - Кнопка "Подтвердить" для генподрядчика
   - Сохранение подписи в БД

Используй библиотеки:
- openpyxl для Excel файлов
- Jinja2 для HTML шаблонов
- WeasyPrint для PDF (если нужно)
```

### ЭТАП 3: МОБИЛЬНОЕ ПРИЛОЖЕНИЕ (3-4 недели)

#### Задача 3.1: Основная структура Flutter приложения
**Для AI-агента:**
```
Создай Flutter приложение со структурой:

lib/
├── main.dart
├── models/ - модели данных
├── services/ - API сервисы  
├── screens/ - экраны приложения
├── widgets/ - переиспользуемые виджеты
├── utils/ - утилиты
└── constants/ - константы

Основные экраны:
1. SplashScreen - загрузочный экран
2. AuthScreen - регистрация/вход
3. RoleSelectionScreen - выбор роли (рабочий/директор)
4. WorkerHomeScreen - главный экран рабочего
5. DirectorHomeScreen - главный экран директора
6. AddWorkScreen - чат для ввода работы
7. WorkListScreen - список записей с фильтрами
8. ProjectsScreen - управление объектами

Используй пакеты:
- http для API запросов
- shared_preferences для хранения токенов
- provider для стейт-менеджмента
- flutter_chat_ui для чата ввода работ
```

#### Задача 3.2: Экран быстрого ввода работ (ключевая функция)
**Для AI-агента:**
```
Создай экран AddWorkScreen с чат-интерфейсом:

Требования:
1. Интерфейс как мессенджер - поле ввода внизу, кнопка отправки
2. После отправки показать "думаю..." пока AI обрабатывает
3. Показать распознанные данные для подтверждения:
   - Дата: [дата]
   - Описание: [описание] 
   - Объем: [количество] [единица]
   - Тип: [часовая/сдельная/штучная]
4. Кнопки "Сохранить" / "Исправить"
5. Возможность быстро повторить похожую запись

Логика работы:
- Отправка текста на /api/ai/parse-work
- Обработка ответа и показ для подтверждения
- Сохранение через POST /api/work-entries/
- Автоматическое определение активного проекта

Принцип "2 клика":
1. Ввод текста + отправка
2. Подтверждение и сохранение

Используй простой, интуитивный UI без лишних элементов.
```

#### Задача 3.3: Экраны для рабочих
**Для AI-агента:**
```
Создай экраны для роли "рабочий":

1. WorkerHomeScreen:
   - Активный проект вверху
   - Кнопка "Добавить работу" (главная, большая)
   - Быстрые действия: "Вчера", "Сегодня" 
   - Последние записи (3-5 штук)
   - Статистика за неделю

2. WorkListScreen:  
   - Фильтры по дате (быстрые: неделя, месяц)
   - Список записей с возможностью редактирования
   - Группировка по дням
   - Свайп для удаления

3. ProjectSelectionScreen:
   - Список доступных проектов
   - Кнопка "Создать новый проект"
   - Отметка активного проекта

4. SettingsScreen:
   - Профиль пользователя
   - Настройки приложения  
   - Экспорт табеля
   - Выход из аккаунта

Все экраны должны работать быстро, с минимумом загрузок.
```

#### Задача 3.4: Экраны для директоров
**Для AI-агента:**
```
Создай экраны для роли "директор":

1. DirectorHomeScreen:
   - Обзор компании (количество рабочих, проектов)
   - Последние обновления от рабочих
   - Быстрые отчеты
   - Уведомления о новых табелях

2. WorkersScreen:
   - Список рабочих компании
   - Статус активности каждого
   - Кнопка "Пригласить рабочего"
   - Переход к детальной информации

3. ReportsScreen:
   - Фильтры: рабочий, проект, период
   - Типы отчетов: табели, сводка, статистика
   - Кнопка "Экспорт" 
   - Отправка по email

4. CompanyManagementScreen:
   - Настройки компании
   - Управление проектами
   - Приглашения рабочих (ID компании)
   - Интеграции

Фокус на получении нужной информации за минимум кликов.
```

### ЭТАП 4: ВЕБ-ПАНЕЛЬ ДИРЕКТОРА (2-3 недели)

#### Задача 4.1: Создание веб-панели на Vue.js
**Для AI-агента:**
```
Создай веб-приложение на Vue.js + Nuxt.js:

Страницы:
1. /login - авторизация  
2. /dashboard - главная панель
3. /workers - управление рабочими
4. /projects - управление проектами
5. /reports - отчеты и аналитика
6. /timesheets - табели для подписания
7. /settings - настройки компании

Компоненты:
- WorkerCard - карточка рабочего
- ProjectCard - карточка проекта  
- ReportTable - таблица отчета
- DateRangePicker - выбор периода
- ExportButton - экспорт данных

Используй:
- Vue 3 + Composition API
- Tailwind CSS для стилизации  
- Chart.js для графиков
- Axios для API запросов
- Vue Router для навигации

Требования:
- Отзывчивый дизайн (работа на планшете)
- Быстрая загрузка страниц
- Интуитивный интерфейс
```

#### Задача 4.2: Система отчетов и аналитики
**Для AI-агента:**
```
Реализуй систему отчетов:

1. Дашборд с ключевыми метриками:
   - Общие часы за период
   - Активные рабочие  
   - Завершенные проекты
   - Графики динамики работ

2. Детальные отчеты:
   - По рабочему: все работы, статистика, эффективность
   - По проекту: все рабочие, объемы, сроки
   - По компании: сводка, тренды, планирование

3. Экспорт данных:
   - Excel файлы для бухгалтерии
   - PDF отчеты для заказчиков
   - CSV для дальнейшей обработки

4. Фильтрация и поиск:
   - По датам, рабочим, проектам
   - Быстрые фильтры (неделя, месяц, квартал)
   - Сохраненные фильтры

Все должно работать быстро с большим объемом данных.
```

### ЭТАП 5: СИСТЕМА ПОДПИСАНИЯ ДЛЯ ГЕНПОДРЯДЧИКОВ (1-2 недели)

#### Задача 5.1: Веб-страница для подписания табелей
**Для AI-агента:**
```
Создай систему для подписания табелей генподрядчиками:

1. Генерация уникальных ссылок:
   - Токен безопасности в URL  
   - Срок действия ссылки (например, 30 дней)
   - Привязка к конкретному табелю

2. Веб-страница подписания (/approve/{token}):
   - Отображение табеля в читаемом виде
   - Информация о рабочем и проекте
   - Детализация выполненных работ
   - Кнопка "Подтвердить выполнение работ"
   - Поле для комментариев (опционально)

3. Обработка подписания:
   - Сохранение факта подписи в БД
   - Отправка уведомления директору
   - Изменение статуса табеля на "Подписан"
   - Логирование действия

4. Email уведомления:
   - Отправка ссылки генподрядчику  
   - Напоминание о неподписанных табелях
   - Уведомление о подписании

Интерфейс должен быть простым - без регистрации и установки приложений.
```

### ЭТАП 6: ИНТЕГРАЦИИ И ДОРАБОТКИ (1-2 недели)

#### Задача 6.1: Настройка уведомлений
**Для AI-агента:**
```
Реализуй систему уведомлений:

1. Push-уведомления в мобильном приложении:
   - Напоминания о заполнении табелей
   - Уведомления директору о новых записях
   - Статус подписания табелей

2. Email уведомления:
   - Еженедельные отчеты директору
   - Ссылки для подписания генподрядчикам
   - Системные уведомления

3. SMS уведомления (опционально):
   - Критически важные уведомления
   - Подтверждение подписания

Используй:
- Firebase для push-уведомлений
- SendGrid/Mailgun для email
- Telegram Bot API как дополнительный канал
```

#### Задача 6.2: Оптимизация и тестирование
**Для AI-агента:**
```
Оптимизируй производительность системы:

1. Кэширование:
   - Redis для частых запросов
   - Кэш отчетов на уровне API
   - Локальный кэш в мобильном приложении

2. Оптимизация БД:
   - Индексы для быстрого поиска
   - Архивирование старых данных
   - Оптимизация сложных запросов

3. Тестирование:
   - Unit тесты для критичной логики
   - Integration тесты для API
   - E2E тесты ключевых пользовательских сценариев

4. Мониторинг:
   - Логирование ошибок (Sentry)
   - Метрики производительности  
   - Мониторинг доступности сервиса

Цель - стабильная работа с большим количеством пользователей.
```

## 4. ПЛАН РАЗВЕРТЫВАНИЯ

### Задача 4.1: Подготовка к продакшену
**Для AI-агента:**
```
Подготовь проект к развертыванию:

1. Контейнеризация:
   - Dockerfile для каждого сервиса
   - docker-compose.yml для продакшена
   - Переменные окружения для конфигурации

2. CI/CD пайплайн:
   - GitHub Actions для автодеплоя
   - Тестирование перед деплоем
   - Автоматический откат при ошибках

3. Безопасность:
   - HTTPS сертификаты
   - Защита API от DDoS
   - Шифрование чувствительных данных
   - Регулярные бэкапы БД

4. Масштабирование:
   - Load balancer для распределения нагрузки
   - Горизонтальное масштабирование API
   - CDN для статических файлов
```

## 5. ВРЕМЕННЫЕ РАМКИ И РЕСУРСЫ

**Общий срок разработки: 10-14 недель**

**Приоритеты для MVP (минимально жизнеспособный продукт):**
1. Backend API с AI-парсингом
2. Мобильное приложение для рабочих  
3. Базовая веб-панель для директоров
4. Простая система подписания

**Второй этап (расширенная версия):**
1. Продвинутая аналитика
2. Интеграции с внешними системами
3. Мобильное приложение для директоров
4. Автоматизация отчетности

## 6. СТОИМОСТЬ РАЗРАБОТКИ

**AI сервисы:**
- OpenAI API: ~$50-100/месяц на начальном этапе
- Альтернатива: Yandex GPT ~$20-50/месяц

**Инфраструктура:**
- VPS сервер: $20-50/месяц
- База данных: $10-30/месяц  
- Файловое хранилище: $5-15/месяц
- Домен и SSL: $20/год

**Разработка с AI-агентами:**
- OpenAI API для разработки: $100-200 за весь проект
- Время: 3-4 месяца при работе с AI-агентами

## 7. КРИТЕРИИ УСПЕХА

**Технические:**
- Время ввода работы < 30 секунд
- Точность AI-парсинга > 90%
- Время отклика API < 500ms
- Доступность системы > 99%

**Пользовательские:**  
- Снижение времени заполнения табелей в 10 раз
- Увеличение точности данных
- Сокращение времени обработки табелей директором
- Упрощение процесса подписания для генподрядчиков

## 8. РИСКИ И МИТИГАЦИЯ

**Технические риски:**
- Неточность AI-парсинга → тестирование на реальных данных
- Производительность с большими объемами → нагрузочное тестирование
- Проблемы интеграции → пошаговое тестирование

**Пользовательские риски:**
- Сопротивление изменениям → простой интерфейс и обучение
- Сложность использования → фокус на UX исследованиях
- Технические проблемы → качественная техподдержка

Этот план обеспечивает создание масштабируемой, но простой в использовании системы, которая решает основные проблемы учета рабочего времени в строительстве.