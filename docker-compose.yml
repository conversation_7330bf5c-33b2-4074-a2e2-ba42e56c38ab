version: "3.8"
services:
  db:
    image: postgres:16-alpine
    container_name: tsai_db
    environment:
      POSTGRES_USER: tsai
      POSTGRES_PASSWORD: tsai_password
      POSTGRES_DB: tsai
    ports:
      - "5432:5432"
    volumes:
      - dbdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U tsai -d tsai"]
      interval: 5s
      timeout: 3s
      retries: 10

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: tsai_backend
    env_file:
      - ./backend/.env
    depends_on:
      db:
        condition: service_healthy
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
    command: ["bash", "-lc", "alembic upgrade head && uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"]

volumes:
  dbdata:
