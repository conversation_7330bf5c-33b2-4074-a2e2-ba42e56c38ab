# WARP.md

This file provides guidance to WARP (warp.dev) when working with code in this repository.

## Project Overview

TimeSheetAI is an AI-powered timesheet management system for the construction industry in Finland. The system consists of:

- **Backend**: FastAPI-based REST API with PostgreSQL database
- **Mobile App**: Flutter application for workers 
- **Web Dashboard**: Vue.js application for directors
- **AI Service**: Natural language processing for work entry parsing

## Development Environment Setup

### Prerequisites
```powershell
# Check Python version (3.11+ required)
python --version

# Check Node.js (18+ required)
node --version

# Check if PostgreSQL is running
Get-Service postgresql*

# Check if Redis is available
redis-cli ping
```

### Initial Setup
```powershell
# Clone and setup project
git clone <repository-url>
cd TimeSheetAI

# Create Python virtual environment
python -m venv .venv
.venv\Scripts\Activate.ps1

# Install backend dependencies
cd backend
pip install -r requirements.txt

# Setup frontend dependencies
cd ..\frontend\web
npm install

cd ..\mobile
flutter pub get
```

### Database Setup
```powershell
# Start PostgreSQL and Redis via Docker
docker-compose up -d postgres redis

# Run database migrations
cd backend
alembic upgrade head

# Seed test data
python scripts/seed_data.py
```

## Common Development Commands

### Backend Development
```powershell
# Start backend server with hot reload
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Run backend tests
pytest

# Run tests with coverage
pytest --cov=app --cov-report=html

# Format code
black .
isort .
flake8 .

# Type checking
mypy app/

# Generate new migration
alembic revision --autogenerate -m "description"

# Apply migrations
alembic upgrade head
```

### Frontend Web Development
```powershell
cd frontend\web

# Start development server
npm run dev

# Build for production
npm run build

# Run tests
npm test

# Run linting
npm run lint

# Type checking
npm run type-check
```

### Mobile Development
```powershell
cd frontend\mobile

# Run on Android emulator
flutter run

# Run on iOS simulator
flutter run -d ios

# Run tests
flutter test

# Build APK
flutter build apk

# Build iOS app
flutter build ios

# Generate code (models, etc.)
flutter packages pub run build_runner build
```

### Docker Operations
```powershell
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f [service-name]

# Restart specific service
docker-compose restart backend

# Stop all services
docker-compose down

# Rebuild and restart
docker-compose up -d --build
```

## Project Architecture

### High-Level Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │    │   Vue.js Web    │    │  Approval Web   │
│   (Workers)     │    │   (Directors)   │    │ (Contractors)   │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼───────────┐
                    │     FastAPI Backend     │
                    │  ┌─────────────────────┐│
                    │  │   AI Parser Service ││
                    │  └─────────────────────┘│
                    └─────────────┬───────────┘
                                  │
                    ┌─────────────▼───────────┐
                    │   PostgreSQL Database   │
                    └─────────────────────────┘
```

### Backend Structure
```
backend/
├── app/
│   ├── main.py              # FastAPI app entry point
│   ├── core/               # Core configuration and utilities
│   │   ├── config.py       # Settings and configuration
│   │   ├── database.py     # Database connection
│   │   └── auth.py         # Authentication logic
│   ├── models/             # SQLAlchemy database models
│   │   ├── user.py         # User model
│   │   ├── company.py      # Company model
│   │   ├── project.py      # Project model
│   │   └── work_entry.py   # Work entry model
│   ├── schemas/            # Pydantic schemas for API
│   ├── api/               # API route handlers
│   │   ├── auth.py        # Authentication endpoints
│   │   ├── users.py       # User management endpoints
│   │   ├── work_entries.py # Work entry endpoints
│   │   └── reports.py     # Report generation endpoints
│   ├── services/          # Business logic services
│   │   ├── ai_parser.py   # AI text parsing service
│   │   ├── user_service.py # User management service
│   │   └── report_service.py # Report generation service
│   └── utils/             # Utility functions
└── tests/                 # Backend tests
```

### Key Features Implementation

#### AI Text Parsing
The system uses OpenAI GPT-4 to parse natural language work descriptions:
```python
# Example: "вчера шлифовал полы 80м2" → 
{
  "date": "2025-09-09",
  "description": "шлифовал полы", 
  "quantity": 80,
  "unit": "м2",
  "work_type": "сдельная"
}
```

#### Multi-tenant Architecture
- Companies are isolated by `company_id`
- Users belong to companies
- Projects are company-specific
- All queries filter by company context

#### Role-based Access Control
- **Workers**: Create and edit their own work entries
- **Directors**: Manage company, view all workers' data, generate reports
- **Contractors**: Approve timesheets via unique links

## Database Schema

### Core Tables
```sql
-- Companies (multi-tenant)
companies (id, name, settings, created_at)

-- Users with roles
users (id, email, name, role, company_id, created_at)
-- role: 'worker' | 'director'

-- Construction projects  
projects (id, name, address, company_id, status, created_at)

-- Work entries (main data)
work_entries (id, user_id, project_id, date, description, 
              work_type, quantity, unit, hours, raw_input,
              created_at, updated_at)

-- Generated timesheets
timesheets (id, user_id, project_id, period_start, period_end,
            status, approval_link, approved_at, created_at)
```

## API Endpoints

### Authentication
```
POST /api/auth/register      # User registration
POST /api/auth/login         # User login
POST /api/auth/refresh       # Token refresh
GET  /api/auth/me           # Current user info
```

### Work Entries
```
POST /api/work-entries/      # Create work entry (with AI parsing)
GET  /api/work-entries/      # List work entries (with filters)
PUT  /api/work-entries/{id}  # Update work entry
DELETE /api/work-entries/{id} # Delete work entry
```

### Companies & Projects
```
GET  /api/companies/my       # Get user's company
POST /api/projects/          # Create project (directors only)
GET  /api/projects/          # List company projects
```

### Reports & Timesheets
```
POST /api/timesheets/generate # Generate timesheet
GET  /api/timesheets/         # List timesheets  
POST /api/reports/export      # Export reports (Excel/PDF)
```

## Testing Strategy

### Backend Testing
```powershell
# Run all tests
pytest

# Run specific test file
pytest tests/test_ai_parser.py

# Run tests with coverage
pytest --cov=app --cov-report=html

# Run integration tests
pytest tests/integration/

# Run only unit tests
pytest tests/unit/
```

### Frontend Testing
```powershell
# Vue.js tests
cd frontend\web
npm test
npm run test:coverage

# Flutter tests
cd frontend\mobile  
flutter test
flutter test --coverage
```

## Deployment

### Local Development
```powershell
# Start all services with Docker
docker-compose up -d

# Or start services individually
# Database
docker-compose up -d postgres redis

# Backend  
cd backend
uvicorn app.main:app --reload

# Frontend
cd frontend\web
npm run dev
```

### Production Deployment
```powershell
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy with environment variables
docker-compose -f docker-compose.prod.yml up -d
```

## Environment Variables

### Backend (.env)
```bash
# Application
APP_NAME=timesheet-ai
DEBUG=false
SECRET_KEY=your-secret-key

# Database  
DATABASE_URL=postgresql://user:password@localhost:5432/timesheet
REDIS_URL=redis://localhost:6379/0

# AI Service
OPENAI_API_KEY=your-openai-key
AI_MODEL=gpt-4

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-password
```

## Code Standards

### Python (Backend)
- Follow PEP 8 strictly
- Use Black for formatting
- Type hints required for all functions
- Docstrings for all public methods
- Russian language for comments and documentation

### TypeScript/Vue.js (Frontend) 
- Use TypeScript for type safety
- Composition API for Vue 3
- Tailwind CSS for styling
- Russian language for UI text (Finnish after MVP)

### Flutter (Mobile)
- Follow Dart conventions
- Use BLoC pattern for state management
- Material Design components
- Comprehensive error handling

## AI Integration Notes

### OpenAI Integration
- Uses GPT-4 for natural language processing
- Fallback mechanisms when AI service unavailable
- Caches successful parsing results
- Handles Finnish and Russian input text

### Parsing Examples
```
Input: "вчера шлифовал полы на 2 этаже 80м2"
Output: {
  date: "2025-09-09",
  description: "шлифовал полы на 2 этаже", 
  quantity: 80,
  unit: "м2",
  work_type: "сдельная"
}

Input: "сегодня работал 8 часов на монтаже"  
Output: {
  date: "2025-09-10",
  description: "работал на монтаже",
  hours: 8, 
  work_type: "часовая"
}
```

## Troubleshooting

### Common Issues

#### Database Connection Issues
```powershell
# Check PostgreSQL status
Get-Service postgresql*

# Reset database connection
docker-compose restart postgres

# Check database logs
docker-compose logs postgres
```

#### AI Service Issues  
```powershell
# Check OpenAI API key
echo $env:OPENAI_API_KEY

# Test AI service endpoint
curl -X POST localhost:8000/api/ai/parse-work -d '{"text":"test"}'
```

#### Frontend Build Issues
```powershell
# Clear node_modules and reinstall
cd frontend\web
Remove-Item -Recurse -Force node_modules
npm install

# Clear Flutter cache
cd frontend\mobile
flutter clean
flutter pub get
```

### Performance Monitoring
- Backend response times should be < 500ms
- AI parsing accuracy should be > 90%
- Database queries optimized with proper indexes
- Redis caching for frequently accessed data

## Security Considerations

- JWT tokens with short expiration (15 minutes)
- Refresh tokens for session management
- Input validation on all API endpoints
- SQL injection prevention via SQLAlchemy ORM
- CORS properly configured for cross-origin requests
- Environment variables for sensitive configuration

This WARP.md provides comprehensive guidance for working with the TimeSheetAI project. Refer to the detailed documentation in the `docs/` folder for more specific implementation details.
