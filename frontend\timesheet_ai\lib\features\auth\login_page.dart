import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import '../../core/http_client.dart';
import 'token_store.dart';

class LoginPage extends StatefulWidget {
  final HttpClient client;
  final void Function(String role) onLoggedIn;
  const LoginPage({super.key, required this.client, required this.onLoggedIn});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  bool loading = false;
  String? msg;

  Future<void> _login() async {
    setState(() { loading = true; msg = null; });
    try {
      final r = await widget.client.dio.post('/api/v1/auth/login', data: {
        'email': emailController.text,
        'password': passwordController.text,
      });
      final token = r.data['access_token'];
      TokenStore.accessToken = token;
      // get current user
      final r2 = await widget.client.dio.get('/api/v1/auth/me', options: Options(headers: {
        'Authorization': 'Bearer $token',
      }));
      final role = r2.data['role'] as String;
      widget.onLoggedIn(role);
    } catch (e) {
      setState(() { msg = e.toString(); });
    } finally {
      setState(() { loading = false; });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Login')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(children: [
          TextField(controller: emailController, decoration: const InputDecoration(labelText: 'Email')),
          TextField(controller: passwordController, decoration: const InputDecoration(labelText: 'Password'), obscureText: true),
          ElevatedButton(onPressed: loading ? null : _login, child: Text(loading ? '...' : 'Login')),
          if (msg != null) Text(msg!),
        ]),
      ),
    );
  }
}
