# Планирование спринтов TimeSheet AI

## Sprint 1: Базовая архитектура и окружение ✅
- Настройка репозитория и структуры проекта
- Docker Compose с PostgreSQL и backend
- FastAPI приложение с healthcheck
- Alembic для миграций БД
- Flutter каркас с HTTP клиентом
- CI/CD с GitHub Actions
- Pre-commit хуки и линтеры

## Sprint 2: Авторизация и роли (планируется)
- JWT аутентификация
- Модели пользователей с ролями
- Регистрация и вход
- Разделение интерфейса по ролям

## Sprint 3: Объекты и компании (планируется)
- Модели компаний и проектов
- Управление рабочими
- Присоединение к компаниям

## Sprint 4: Работы - основная функция (планируется)
- Модель записей о работах
- NLP парсинг естественного языка
- CRUD операции для работ
- Чат-интерфейс для ввода

## Sprint 5: Экспорт tuntilist (планируется)
- Генерация табелей
- Экспорт в Excel/PDF
- Система подписания

## Sprint 6: Минимальный UI/UX (планируется)
- Финальная доработка интерфейса
- Оптимизация пользовательского опыта
- Локализация
