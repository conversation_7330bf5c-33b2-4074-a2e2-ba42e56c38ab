# Руководство по ведению проекта TimeSheet AI

## Версия: 1.0
## Дата: 31 августа 2025

---

## 1. ОБЩИЕ ПРИНЦИПЫ ПРОЕКТА

### 1.1 Миссия проекта
Создать интуитивно понятную систему учета рабочего времени, которая упростит процесс документооборота в строительной отрасли и повысит эффективность работы всех участников процесса.

### 1.1.1 Требования к проекту.
Данное разрабатываемое приложение будет локализовано в Финляндии. Вся документация и коментарии к коду должны вестись на русском языке. Единицы измерения, валюта, адреса и т.д. должны быть адаптированны под финский рынок. Вводимые данные рабочими и директорами (пользователи) в БД будут на финском и русском языках. Выводимые данные на экране и в отчетах будут на финском языке. На стадии разработки интерфейс приложения будет на русском языке. После успешного запуска на русском языке, в приложение будут добавлены финские и английские языки.

### 1.2 Основные принципы разработки

#### **KISS (Keep It Simple, Stupid)**
- Простота решения важнее технической изощренности
- Каждая функция должна решать конкретную проблему пользователя
- Интерфейс должен быть интуитивно понятен без обучения

#### **DRY (Don't Repeat Yourself)**
- Избегаем дублирования кода и логики
- Создаем переиспользуемые компоненты и функции
- Единые стандарты для похожих задач

#### **YAGNI (You Aren't Gonna Need It)**
- Разрабатываем только необходимый функционал
- Избегаем преждевременной оптимизации
- Фокус на MVP, расширение функционала по потребности

#### **Fail Fast**
- Раннее выявление и исправление ошибок
- Частое тестирование на каждом этапе
- Быстрая итерация и получение обратной связи

---

## 2. СТРУКТУРА ПРОЕКТА

### 2.1 Организация репозитория

```
timesheet-ai/
├── README.md                    # Описание проекта и инструкции запуска
├── CHANGELOG.md                 # История изменений
├── LICENSE                      # Лицензия проекта
├── .gitignore                   # Игнорируемые файлы
├── docker-compose.yml           # Конфигурация контейнеров
├── docs/                        # Документация проекта
│   ├── api/                     # API документация
│   ├── architecture/            # Архитектурные решения
│   ├── deployment/              # Инструкции по развертыванию
│   ├── user-guides/             # Руководства пользователя
│   └── development/             # Документация для разработчиков
├── backend/                     # Backend приложение
│   ├── app/                     # Основной код приложения
│   ├── tests/                   # Тесты
│   ├── alembic/                 # Миграции БД
│   ├── requirements.txt         # Python зависимости
│   └── Dockerfile               # Docker образ
├── frontend/
│   ├── mobile/                  # Flutter мобильное приложение
│   └── web/                     # Vue.js веб-приложение
├── infrastructure/              # Инфраструктурный код
│   ├── terraform/               # IaC конфигурации
│   └── kubernetes/              # K8s манифесты
└── scripts/                     # Utility скрипты
```

### 2.2 Ветвление (Git Flow)

#### **Основные ветки:**
- `main` - стабильная продакшен версия
- `develop` - интеграционная ветка для разработки
- `release/x.x.x` - подготовка к релизу
- `hotfix/x.x.x` - критические исправления

#### **Рабочие ветки:**
- `feature/TAS-123-user-authentication` - новый функционал
- `bugfix/TAS-124-fix-login-error` - исправление багов
- `docs/TAS-125-api-documentation` - обновление документации

#### **Правила именования веток:**
```
[тип]/TAS-[номер_задачи]-[краткое_описание]

Типы:
- feature/   - новый функционал
- bugfix/    - исправление ошибок
- hotfix/    - критические исправления
- docs/      - документация
- refactor/  - рефакторинг кода
- test/      - добавление тестов
```

---

## 3. СТАНДАРТЫ НАПИСАНИЯ КОДА

### 3.1 Python (Backend)

#### **Стиль кода:**
- Следуем **PEP 8** строго
- Используем **Black** для форматирования
- **isort** для упорядочивания импортов
- **flake8** для проверки стиля
- **mypy** для типизации

#### **Структура файлов:**
```python
"""
Модуль для работы с пользователями.

Этот модуль содержит функции для создания, обновления
и управления пользователями системы.
"""

# Стандартные библиотеки
import logging
from datetime import datetime
from typing import List, Optional

# Сторонние библиотеки
from fastapi import HTTPException
from sqlalchemy.orm import Session

# Локальные импорты
from app.core.config import settings
from app.models.user import User
from app.schemas.user import UserCreate


logger = logging.getLogger(__name__)


class UserService:
    """Сервис для работы с пользователями."""
    
    def __init__(self, db: Session) -> None:
        """Инициализация сервиса."""
        self.db = db
    
    def create_user(self, user_data: UserCreate) -> User:
        """
        Создает нового пользователя.
        
        Args:
            user_data: Данные для создания пользователя
            
        Returns:
            Созданный пользователь
            
        Raises:
            HTTPException: Если пользователь уже существует
        """
        # Реализация метода
        pass
```

#### **Правила именования:**
- **Константы**: `MAX_UPLOAD_SIZE = 10_000_000`
- **Переменные**: `user_count`, `is_active`
- **Функции**: `get_user_by_id()`, `calculate_total_hours()`
- **Классы**: `UserService`, `WorkEntryModel`
- **Файлы**: `user_service.py`, `work_entry.py`

#### **Типизация (обязательно):**
```python
from typing import List, Optional, Dict, Any

def get_users(
    db: Session, 
    skip: int = 0, 
    limit: int = 100
) -> List[User]:
    """Получение списка пользователей."""
    return db.query(User).offset(skip).limit(limit).all()

def process_work_entry(
    entry_text: str,
    user_id: int
) -> Optional[Dict[str, Any]]:
    """Обработка записи о работе."""
    # Реализация
    pass
```

#### **Обработка ошибок:**
```python
import logging
from fastapi import HTTPException, status

logger = logging.getLogger(__name__)

def get_user_by_id(db: Session, user_id: int) -> User:
    """Получение пользователя по ID."""
    try:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Пользователь с ID {user_id} не найден"
            )
        return user
    except Exception as e:
        logger.error(f"Ошибка получения пользователя {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Внутренняя ошибка сервера"
        )
```

### 3.2 Flutter (Mobile)

#### **Структура проекта:**
```
lib/
├── main.dart
├── core/
│   ├── constants/
│   ├── errors/
│   ├── network/
│   └── utils/
├── features/
│   ├── authentication/
│   │   ├── data/
│   │   ├── domain/
│   │   └── presentation/
│   └── work_entries/
└── shared/
    ├── widgets/
    └── services/
```

#### **Правила именования:**
- **Файлы**: `user_service.dart`, `work_entry_screen.dart`
- **Классы**: `UserService`, `WorkEntryScreen`
- **Переменные**: `userName`, `isLoading`
- **Константы**: `kPrimaryColor`, `kDefaultPadding`

#### **Пример класса:**
```dart
/// Сервис для работы с записями о работе
class WorkEntryService {
  final ApiClient _apiClient;
  final Logger _logger;

  const WorkEntryService({
    required ApiClient apiClient,
    required Logger logger,
  }) : _apiClient = apiClient, _logger = logger;

  /// Создает новую запись о работе
  Future<WorkEntry> createWorkEntry({
    required String rawText,
    required int userId,
  }) async {
    try {
      final response = await _apiClient.post(
        '/work-entries',
        data: {
          'raw_text': rawText,
          'user_id': userId,
        },
      );
      return WorkEntry.fromJson(response.data);
    } catch (e) {
      _logger.error('Ошибка создания записи о работе: $e');
      rethrow;
    }
  }
}
```

### 3.3 Vue.js (Web Frontend)

#### **Структура компонента:**
```vue
<template>
  <div class="work-entry-form">
    <h2 class="work-entry-form__title">
      Добавить запись о работе
    </h2>
    
    <form 
      class="work-entry-form__form" 
      @submit.prevent="handleSubmit"
    >
      <textarea
        v-model="workText"
        class="work-entry-form__input"
        placeholder="Опишите выполненную работу..."
        :disabled="isLoading"
      />
      
      <button 
        type="submit" 
        class="work-entry-form__submit"
        :disabled="isLoading || !workText.trim()"
      >
        {{ isLoading ? 'Обработка...' : 'Добавить' }}
      </button>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useWorkEntries } from '@/composables/useWorkEntries'

interface Props {
  userId: number
}

const props = defineProps<Props>()

const workText = ref('')
const isLoading = ref(false)

const { createWorkEntry } = useWorkEntries()

const handleSubmit = async (): Promise<void> => {
  if (!workText.value.trim()) return
  
  try {
    isLoading.value = true
    await createWorkEntry({
      rawText: workText.value,
      userId: props.userId
    })
    workText.value = ''
  } catch (error) {
    console.error('Ошибка создания записи:', error)
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped lang="scss">
.work-entry-form {
  &__title {
    @apply text-2xl font-bold mb-4;
  }
  
  &__form {
    @apply space-y-4;
  }
  
  &__input {
    @apply w-full p-3 border rounded-lg resize-none;
    min-height: 120px;
    
    &:disabled {
      @apply opacity-50 cursor-not-allowed;
    }
  }
  
  &__submit {
    @apply bg-blue-600 text-white px-6 py-2 rounded-lg;
    @apply hover:bg-blue-700 transition-colors;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
  }
}
</style>
```

---

## 4. УПРАВЛЕНИЕ ВЕРСИЯМИ И РЕЛИЗЫ

### 4.1 Семантическое версионирование (SemVer)

**Формат:** `MAJOR.MINOR.PATCH` (например, `1.2.3`)

- **MAJOR** - breaking changes (несовместимые изменения API)
- **MINOR** - новый функционал (обратная совместимость)
- **PATCH** - исправления багов

#### **Примеры:**
- `1.0.0` - первый стабильный релиз
- `1.1.0` - добавлена функция экспорта отчетов
- `1.1.1` - исправлена ошибка в парсинге дат
- `2.0.0` - изменен формат API (breaking change)

### 4.2 Changelog

Каждое изменение документируется в `CHANGELOG.md`:

```markdown
# Changelog

## [1.2.0] - 2025-09-15

### Added
- Функция экспорта табелей в PDF формат
- Поддержка multiple проектов для одного рабочего
- Push-уведомления для мобильного приложения

### Changed
- Улучшен алгоритм парсинга естественного языка
- Обновлен дизайн главного экрана приложения

### Fixed
- Исправлена ошибка с некорректным отображением дат
- Устранена проблема с синхронизацией данных

### Security
- Обновлены зависимости с уязвимостями
- Добавлена дополнительная валидация входных данных
```

---

## 5. ТЕСТИРОВАНИЕ

### 5.1 Пирамида тестирования

```
    🔺 E2E Tests (5%)
   🔺🔺 Integration Tests (15%)
  🔺🔺🔺 Unit Tests (80%)
```

### 5.2 Покрытие тестами

**Обязательное покрытие:**
- **Критическая бизнес-логика**: 100%
- **API эндпоинты**: 95%
- **Утилиты и helpers**: 90%
- **UI компоненты**: 80%

### 5.3 Типы тестов

#### **Unit Tests (Python):**
```python
import pytest
from app.services.ai_parser import AIParserService

class TestAIParserService:
    """Тесты для сервиса парсинга AI."""
    
    def setup_method(self):
        """Подготовка перед каждым тестом."""
        self.parser = AIParserService()
    
    def test_parse_work_with_square_meters(self):
        """Тест парсинга работы с квадратными метрами."""
        # Arrange
        input_text = "вчера шлифовал полы 80м2"
        
        # Act
        result = self.parser.parse_work_entry(input_text)
        
        # Assert
        assert result.quantity == 80
        assert result.unit == "м2"
        assert "шлифовал полы" in result.description
        assert result.work_type == "сдельная"
    
    @pytest.mark.parametrize("input_text,expected_hours", [
        ("работал 8 часов", 8),
        ("4 часа монтажа", 4),
        ("полный день 12 часов", 12),
    ])
    def test_parse_hours(self, input_text, expected_hours):
        """Тест парсинга часов работы."""
        result = self.parser.parse_work_entry(input_text)
        assert result.hours == expected_hours
```

#### **Integration Tests:**
```python
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

class TestWorkEntryAPI:
    """Интеграционные тесты API записей о работе."""
    
    def test_create_work_entry_success(self, auth_headers):
        """Тест успешного создания записи о работе."""
        # Arrange
        payload = {
            "raw_text": "сегодня шлифовал полы 50м2",
            "project_id": 1
        }
        
        # Act
        response = client.post(
            "/api/work-entries/",
            json=payload,
            headers=auth_headers
        )
        
        # Assert
        assert response.status_code == 201
        data = response.json()
        assert data["quantity"] == 50
        assert data["unit"] == "м2"
```

---

## 6. ДОКУМЕНТАЦИЯ

### 6.1 Обязательные документы

#### **README.md**
```markdown
# TimeSheet AI

Умная система учета рабочего времени для строительства

## Быстрый старт

### Требования
- Python 3.11+
- Node.js 18+
- PostgreSQL 14+
- Redis 6+

### Установка
```bash
# Клонирование репозитория
git clone https://github.com/company/timesheet-ai.git
cd timesheet-ai

# Запуск через Docker
docker-compose up -d
```

### Использование
- Backend API: http://localhost:8000
- Web UI: http://localhost:3000
- API Docs: http://localhost:8000/docs

### Тестирование
```bash
# Backend тесты
cd backend && pytest

# Frontend тесты
cd frontend/web && npm test
```
```

#### **API Документация**
- Автогенерируемая через FastAPI Swagger
- Дополнительные примеры в `docs/api/`
- Postman коллекции для тестирования

#### **Архитектурная документация**
```markdown
# Архитектура системы

## Общая схема

```mermaid
graph TB
    A[Mobile App] --> D[API Gateway]
    B[Web App] --> D
    C[Approval Web] --> D
    D --> E[Backend Services]
    E --> F[PostgreSQL]
    E --> G[Redis Cache]
    E --> H[AI Service]
```

## Компоненты

### Backend Services
- **FastAPI** - основной API сервер
- **Celery** - фоновые задачи
- **AI Parser** - обработка естественного языка

### Базы данных
- **PostgreSQL** - основное хранилище
- **Redis** - кэширование и сессии
```

---

## 7. БЕЗОПАСНОСТЬ

### 7.1 Требования безопасности

#### **Аутентификация и авторизация:**
- JWT токены с коротким временем жизни (15 минут)
- Refresh токены (7 дней)
- Роле-базированый контроль доступа (RBAC)

#### **Защита данных:**
- Шифрование чувствительных данных в БД
- HTTPS для всех соединений
- Валидация всех входных данных

#### **API Security:**
```python
from fastapi import Security, HTTPException, status
from fastapi.security import HTTPBearer
from app.core.auth import verify_jwt_token

security = HTTPBearer()

async def get_current_user(
    token: str = Security(security)
) -> User:
    """Получение текущего пользователя из JWT токена."""
    try:
        payload = verify_jwt_token(token.credentials)
        user_id = payload.get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Недействительный токен"
            )
        return await get_user_by_id(user_id)
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Недействительный токен"
        )
```

### 7.2 Логирование и мониторинг

#### **Структурированное логирование:**
```python
import structlog
from app.core.config import settings

logger = structlog.get_logger()

def log_user_action(user_id: int, action: str, details: dict):
    """Логирование действий пользователя."""
    logger.info(
        "user_action",
        user_id=user_id,
        action=action,
        details=details,
        timestamp=datetime.utcnow().isoformat()
    )
```

---

## 8. ПРОЦЕСС РАЗРАБОТКИ

### 8.1 Workflow разработчика

1. **Получение задачи** из Jira/GitHub Issues
2. **Создание ветки** от `develop`
3. **Разработка** с TDD подходом
4. **Самотестирование** и код-ревью
5. **Pull Request** с описанием изменений
6. **Code Review** от коллег
7. **Merge** в `develop` после апрува

### 8.2 Definition of Done (DoD)

Задача считается выполненной когда:

- [ ] Код написан согласно стандартам
- [ ] Покрытие тестами соответствует требованиям
- [ ] Все тесты проходят успешно
- [ ] Документация обновлена
- [ ] Code review пройден
- [ ] Функционал протестирован вручную
- [ ] Нет критических уязвимостей безопасности

### 8.3 Code Review чеклист

**Функциональность:**
- [ ] Код делает то, что должен
- [ ] Край-кейсы обработаны
- [ ] Ошибки обрабатываются корректно

**Читаемость:**
- [ ] Код понятен без комментариев
- [ ] Имена переменных и функций выразительны
- [ ] Сложные участки прокомментированы

**Производительность:**
- [ ] Нет очевидных проблем с производительностью
- [ ] Использование памяти оптимально
- [ ] Запросы к БД оптимизированы

**Безопасность:**
- [ ] Входные данные валидируются
- [ ] Нет SQL инъекций
- [ ] Чувствительная информация не логируется

---

## 9. КОНФИГУРАЦИЯ ОКРУЖЕНИЙ

### 9.1 Переменные окружения

#### **Backend (.env):**
```bash
# Приложение
APP_NAME=timesheet-ai
APP_VERSION=1.0.0
DEBUG=false
SECRET_KEY=your-secret-key

# База данных
DATABASE_URL=postgresql://user:password@localhost:5432/timesheet
REDIS_URL=redis://localhost:6379/0

# AI сервис
OPENAI_API_KEY=your-openai-key
AI_MODEL=gpt-4

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-password

# Мониторинг
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=INFO
```

### 9.2 Конфигурация по окружениям

#### **Development:**
- Детальное логирование
- Отключение кэширования
- Автоперезагрузка при изменениях
- Тестовые данные

#### **Staging:**
- Аналог продакшена
- Интеграционные тесты
- Мониторинг производительности

#### **Production:**
- Минимальное логирование
- Все оптимизации включены
- Мониторинг и алерты
- Резервное копирование

---

## 10. МОНИТОРИНГ И ОТЛАДКА

### 10.1 Метрики приложения

**Ключевые метрики:**
- Время отклика API (< 500ms)
- Успешность AI-парсинга (> 90%)
- Активные пользователи
- Количество созданных записей
- Ошибки и исключения

### 10.2 Логирование

**Уровни логирования:**
- `DEBUG` - детальная информация для разработки
- `INFO` - общая информация о работе приложения
- `WARNING` - предупреждения о потенциальных проблемах
- `ERROR` - ошибки, не приводящие к остановке
- `CRITICAL` - критические ошибки, требующие немедленного внимания

**Пример структурированного лога:**
```json
{
  "timestamp": "2025-08-31T10:30:00Z",
  "level": "INFO",
  "service": "backend",
  "user_id": 123,
  "action": "create_work_entry",
  "duration_ms": 245,
  "success": true,
  "details": {
    "raw_text": "сегодня работал 8 часов",
    "parsed_hours": 8,
    "project_id": 456
  }
}
```

---

## 11. ОБНОВЛЕНИЕ ДОКУМЕНТАЦИИ

### 11.1 Ответственность

**Backend разработчик:**
- API документация
- Архитектурные решения
- Инструкции по развертыванию

**Frontend разработчик:**
- UI/UX документация
- Руководства пользователя
- Описание компонентов

**DevOps:**
- Инфраструктурная документация
- Процедуры мониторинга
- Disaster recovery планы

### 11.2 Процесс обновления

1. **Изменение кода** → обновление документации
2. **Еженедельный аудит** документации на актуальность
3. **Квартальный обзор** архитектурных документов
4. **Обязательная** документация для публичных API

---

## 12. ЗАКЛЮЧЕНИЕ

Данное руководство является живым документом и должно обновляться по мере развития проекта. Все участники команды обязаны следовать этим стандартам для обеспечения качества, безопасности и поддерживаемости продукта.

**Помните:** хороший код должен быть понятен не только машине, но и человеку, который будет его поддерживать через год.

---

*Последнее обновление: 31 августа 2025*  
*Версия документа: 1.0*