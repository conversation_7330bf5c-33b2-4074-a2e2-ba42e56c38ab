---

# 📌 Техническое задание для разработки MVP

## 🔹 Спринт 1. Базовая архитектура и окружение

**Задачи:**

1. Настроить репозиторий (GitHub/GitLab).

   * Создать папки `backend/`, `frontend/`, `docs/`.
   * Настроить `.gitignore` для Python, Flutter, Docker.
   * Подключить линтеры (flake8, black).

2. Подготовить **бэкенд (FastAPI)**:

   * Создать структуру проекта (`app/` → `models`, `routes`, `services`).
   * Подключить PostgreSQL через SQLAlchemy.
   * Настроить Alembic для миграций.
   * Сделать конфиг (dotenv).

3. Подготовить **фронтенд (Flutter)**:

   * Создать базовый проект.
   * Настроить архитектуру (MVVM или BLoC).
   * Подключить Dio (HTTP-клиент).

---

## 🔹 Спринт 2. Авторизация и роли

**Задачи:**

1. Реализовать **регистрацию и вход** (FastAPI + JWT).

   * Таблица `users`: id, имя, роль (рабочий/директор), email, пароль (hash).
   * Роуты: `/auth/register`, `/auth/login`.

2. Реализовать **две роли**:

   * рабочий (worker),
   * директор (director).

3. На фронтенде:

   * Экран регистрации, входа.
   * После входа → проверка роли и редирект на рабочий экран или директорский.

---

## 🔹 Спринт 3. Объекты и компании

**Задачи:**

1. Таблица `companies`: id, название, директор\_id.

2. Таблица `objects`: id, название, адрес, company\_id.

3. Возможности:

   * директор создаёт компанию,
   * рабочий может присоединиться по ID компании,
   * директор может подтверждать рабочих.

4. Фронтенд:

   * У рабочего: экран выбора компании (или работа без компании).
   * У директора: экран создания компании и список работников.

---

## 🔹 Спринт 4. Работы (основная функция)

**Задачи:**

1. Таблица `works`:

   * id, worker\_id, object\_id, дата, описание, количество, единица измерения, тип работы (часовая, м², штуки).

2. Функции рабочего:

   * «Добавить работу» → чат-поле для ввода текста.
   * NLP-модуль: распарсить ввод (например, «вчера шлифовал полы 80м2» → дата=вчера, описание=шлифовка, количество=80, единица=м²).
   * CRUD: добавить, редактировать, удалить, просмотреть список.

3. Фронтенд:

   * Экран добавления (имитация чата).
   * Список работ по датам.
   * Фильтр по дате.

---

## 🔹 Спринт 5. Экспорт tuntilist

**Задачи:**

1. Реализовать API для экспорта:

   * `/export/worker/{id}` → PDF/Excel по одному работнику.
   * `/export/object/{id}` → по объекту.
   * `/export/company/{id}` → сводный.

2. Формат tuntilist:

   * название компании,
   * имя рабочего,
   * объект,
   * период дат,
   * таблица (дата, описание, количество, тип),
   * поле «подпись заказчика».

3. Фронтенд:

   * У рабочего: кнопка «Экспорт».
   * У директора: экспорт по работнику/объекту/компании.

---

## 🔹 Спринт 6. Минимальный UI/UX

**Задачи:**

1. Рабочий:

   * Быстрый ввод работы (одним экраном → «как смс»).
   * Список работ.
   * Кнопка «экспорт».

2. Директор:

   * Список работников.
   * Экспорт данных.

3. Общие требования:

   * максимально простой интерфейс,
   * минимум кликов,
   * русский язык (локализация через `intl`).

---

# 📌 Итог MVP

* Рабочий может зарегистрироваться, добавить работу через чат, просмотреть и экспортировать tuntilist.
* Директор может создать компанию, добавить рабочих, экспортировать отчёты.
* Данные хранятся в PostgreSQL.
* NLP-модуль парсит простые вводы (на старте через regex + dateparser, потом можно заменить на GPT).

---
