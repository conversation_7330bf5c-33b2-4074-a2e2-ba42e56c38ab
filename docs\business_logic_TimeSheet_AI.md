# Детальное описание бизнес-логики приложения TimeSheet AI

## Общая архитектура системы

### Роли пользователей и их возможности

**1. Рабочий (Worker)**
- Добавление записей о работе через чат-интерфейс
- Просмотр и редактирование истории работ
- Управление активными проектами
- Экспорт табелей для подписания
- Синхронизация данных между устройствами

**2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Director)**
- Создание и управление компанией
- Приглашение и управление рабочими
- Просмотр аналитики и отчетности
- Управление проектами и объектами
- Мониторинг активности рабочих в реальном времени

**3. Генподрядчик (Contractor)**
- Подписание табелей через веб-интерфейс
- Просмотр подтвержденных работ
- Получение уведомлений о новых табелях

## Детальное описание экранов мобильного приложения (Flutter)

### 1. Экран запуска (SplashScreen)
- Отображение логотипа TimeSheet AI
- Проверка авторизации пользователя
- Автоматический переход на соответствующий экран
- Загрузка необходимых данных при наличии интернета

### 2. Экран выбора роли (RoleSelectionScreen)
- Кнопка "Я рабочий" - переход к регистрации/входу рабочего
- Кнопка "Я директор" - переход к регистрации/входу директора
- Информация о возможностях каждой роли
- Возможность переключения роли после регистрации

### 3. Экран авторизации (AuthScreen)
**Для рабочего:**
- Поля: email/телефон, пароль
- Кнопка "Зарегистрироваться"
- Кнопка "Войти"
- Ссылка "Забыли пароль?"
- Поле для ввода ID компании (при присоединении)

**Для директора:**
- Поля: email, пароль, название компании
- Кнопка "Создать компанию"
- Кнопка "Войти в существующую"
- Валидация данных в реальном времени

### 4. Главный экран рабочего (WorkerHomeScreen)
**Верхняя панель:**
- Приветствие с именем пользователя
- Текущий активный проект (выпадающий список)
- Кнопка смены проекта/добавления нового

**Центральная часть:**
- Большая кнопка "Добавить работу" (основной action)
- Быстрые действия: "Вчера", "Сегодня", "8 часов"
- Статистика за текущую неделю: общее время, объем работ

**Нижняя часть:**
- Последние 5 записей о работах (дата, проект, описание)
- Свайп-действия: редактирование, удаление
- Индикатор синхронизации с сервером

### 5. Экран добавления работы (AddWorkScreen) - Ключевой экран
**Интерфейс чата:**
- Поле ввода в нижней части экрана
- Кнопка отправки (иконка микрофона для голосового ввода)
- История сообщений с временными метками

**Процесс добавления:**
1. Пользователь вводит текст: "вчера шлифовал полы 80м2"
2. Отправка на сервер, отображение "ИИ обрабатывает..."
3. Получение и отображение распознанных данных:
   - Дата: 30.08.2025 (с возможностью корректировки)
   - Проект: [текущий активный] (с возможностью смены)
   - Описание: "Шлифовка полов" (с возможностью редактирования)
   - Количество: 80
   - Единица: м²
   - Тип работы: сдельная
4. Кнопки "Сохранить" и "Исправить вручную"

**Особенности:**
- Автодополнение часто используемых фраз
- Сохранение черновиков при прерывании
- Оффлайн-работа с последующей синхронизацией
- Валидация введенных данных

### 6. Экран списка работ (WorkListScreen)
**Фильтры:**
- Быстрые периоды: "Сегодня", "Неделя", "Месяц"
- Выбор даты начала и окончания
- Фильтр по проекту
- Фильтр по типу работы

**Список работ:**
- Группировка по дням
- Для каждой записи: дата, проект, описание, количество
- Свайп влево: редактирование
- Свайп вправо: удаление (с подтверждением)
- Возможность множественного выбора для экспорта

**Статистика:**
- Общее количество часов/объем работ за период
- График распределения работ по дням
- Экспорт в Excel/PDF

### 7. Экран редактирования работы (WorkEditScreen)
- Форма с полями: дата, проект, описание, количество, единицы, тип работы
- Валидация каждого поля
- Кнопка "Сохранить изменения"
- Кнопка "Отмена"
- История изменений записи

### 8. Экран управления проектами (ProjectsScreen)
- Список всех проектов пользователя
- Индикатор активного проекта
- Для каждого проекта: название, адрес, статус
- Кнопка "Создать новый проект"
- Возможность архивирования проектов

### 9. Экран создания/редактирования проекта (ProjectEditScreen)
**Поля:**
- Название проекта (обязательное)
- Адрес объекта
- Описание (опционально)
- Дата начала и окончания (опционально)
- Статус (активный, завершен, архивный)

**Валидация:**
- Проверка уникальности названия проекта
- Корректность дат
- Автозаполнение адреса через карты

### 10. Экран экспорта табелей (ExportScreen)
**Выбор параметров:**
- Период (по умолчанию текущая неделя)
- Формат экспорта: Excel, PDF
- Включение подписи заказчика

**Предпросмотр:**
- Отображение табеля в выбранном формате
- Информация: компания, рабочий, объект, период
- Таблица с работами: дата, описание, количество, тип
- Место для подписи генподрядчика

**Отправка:**
- Экспорт в файл
- Отправка по email
- Генерация ссылки для подписания

### 11. Экран профиля (ProfileScreen)
**Информация:**
- ФИО, должность, контакты
- Название компании, ID сотрудника
- Статистика: всего работ, активных проектов

**Настройки:**
- Язык интерфейса (русский, финский, английский)
- Уведомления: push, email
- Автосинхронизация
- Оффлайн-режим

**Действия:**
- Редактирование профиля
- Смена пароля
- Выход из аккаунта

## Детальное описание веб-панели директора (Vue.js)

### 1. Дашборд (Dashboard)
**Ключевые метрики:**
- Количество активных рабочих
- Общее количество часов за сегодня/неделю/месяц
- Количество активных проектов
- Статус табелей на подписании

**Графики:**
- Распределение работ по типам
- Динамика часов по дням
- Продуктивность рабочих (сравнительная)

**Последние действия:**
- Недавно добавленные работы
- Табели, ожидающие подписания
- Уведомления системы

### 2. Управление рабочими (WorkersScreen)
**Список рабочих:**
- ФИО, должность, контакты
- Статус (активен, неактивен)
- Дата последней активности
- Количество работ за период

**Действия:**
- Добавление нового рабочего (вручную или импорт)
- Редактирование данных
- Деактивация/активация
- Просмотр детальной статистики

**Детальная страница рабочего:**
- Полная статистика работ
- График продуктивности
- История табелей
- Возможность экспорта данных

### 3. Управление проектами (ProjectsScreen)
**Список проектов:**
- Название, адрес, статус
- Ответственный прораб
- Дата начала/окончания
- Бюджет/фактические затраты

**Детальная страница проекта:**
- Общая информация
- Список рабочих на проекте
- График выполнения работ
- Финансовая аналитика

### 4. Система отчетности (ReportsScreen)
**Типы отчетов:**
- По рабочим: эффективность, отработанное время
- По проектам: выполнение, финансовые показатели
- Сводные: по компании, периоду, типам работ

**Генерация отчетов:**
- Выбор параметров: период, рабочие, проекты
- Предпросмотр в различных форматах
- Настройка автоматической отправки
- Шаблоны отчетов

### 5. Табели на подписании (TimesheetsScreen)
**Список табелей:**
- Период, рабочий, проект
- Статус (ожидает, подписан, отклонен)
- Дата отправки на подписание

**Действия:**
- Просмотр табеля
- Отправка напоминания генподрядчику
- Отмена подписания
- Экспорт подписанных табелей

### 6. Настройки компании (CompanySettingsScreen)
**Общая информация:**
- Название компании, реквизиты
- Логотип, контактные данные
- Настройки локализации

**Рабочие процессы:**
- Настройка типов работ и единиц измерения
- Стандартные формулировки работ
- Шаблоны табелей и отчетов

**Интеграции:**
- Настройка email уведомлений
- Интеграция с бухгалтерскими системами
- API доступ для разработчиков

## Бизнес-логика и сценарии использования

### Сценарий 1: Рабочий добавляет запись о работе
1. Открывает приложение → видит главный экран
2. Нажимает "Добавить работу" → открывается чат-интерфейс
3. Пишет: "вчера штукатурил стены 50м2"
4. Нажимает отправить → видит "ИИ обрабатывает..."
5. Видит распознанные данные:
   - Дата: [вчерашняя]
   - Проект: [текущий активный]
   - Описание: "Штукатурка стен"
   - Количество: 50
   - Единица: м²
   - Тип: сдельная
6. Подтверждает → запись сохраняется
7. Получает уведомление об успешном сохранении

### Сценарий 2: Директор проверяет отчетность
1. Входит в веб-панель → видит дашборд
2. Переходит в "Отчеты" → выбирает "По рабочим за неделю"
3. Выбирает период и нужных рабочих
4. Генерирует отчет в Excel
5. Проверяет данные, при необходимости корректирует
6. Экспортирует для бухгалтерии
7. Настраивает автоматическую отправку отчета на email

### Сценарий 3: Генподрядчик подписывает табель
1. Получает email со ссылкой на табель
2. Переходит по ссылке → открывается веб-страница
3. Видит табель в читаемом формате:
   - Информация о компании и рабочем
   - Период работ
   - Детализированный список работ
   - Итоговые показатели
4. Проверяет корректность данных
5. Нажимает "Подтвердить" → статус меняется на "Подписан"
6. Получает подтверждение по email
7. Директор получает уведомление о подписании

## Интеграционные моменты и особенности

### Синхронизация данных
- Фоновая синхронизация при наличии интернета
- Конфликт-менеджмент при редактировании одних данных с разных устройств
- Индикатор статуса синхронизации
- Принудительная синхронизация по требованию

### Оффлайн-работа
- Возможность добавления работ без интернета
- Локальное хранение данных
- Автоматическая синхронизация при появлении соединения
- Валидация данных перед синхронизацией

### Безопасность и доступ
- JWT-аутентификация с коротким временем жизни
- Ролевая модель доступа
- Шифрование чувствительных данных
- Резервное копирование и история изменений

### Уведомления и напоминания
- Push-уведомления о необходимости заполнить табель
- Email напоминания о неподписанных табелях
- Уведомления о новых рабочих/проектах
- Системные уведомления об ошибках

Это детальное описание покрывает все аспекты бизнес-логики приложения, обеспечивая полное понимание функциональности для разработки и реализации проекта.