#!/usr/bin/env python3
"""
Ручной тест для проверки работоспособности TimeSheet AI
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_imports():
    """Тест импорта основных модулей"""
    try:
        from app.main import app
        from app.core.config import settings
        from app.api.v1.routes import router
        print("✅ Все модули импортируются успешно")
        return True
    except Exception as e:
        print(f"❌ Ошибка импорта: {e}")
        return False

def test_config():
    """Тест конфигурации"""
    try:
        from app.core.config import settings
        print(f"✅ Конфигурация загружена:")
        print(f"   - PROJECT_NAME: {settings.PROJECT_NAME}")
        print(f"   - ENVIRONMENT: {settings.ENVIRONMENT}")
        print(f"   - API_V1_PREFIX: {settings.API_V1_PREFIX}")
        return True
    except Exception as e:
        print(f"❌ Ошибка конфигурации: {e}")
        return False

def test_routes():
    """Тест маршрутов"""
    try:
        from app.api.v1.routes import router
        routes = [route.path for route in router.routes]
        print(f"✅ Маршруты загружены: {routes}")
        return True
    except Exception as e:
        print(f"❌ Ошибка маршрутов: {e}")
        return False

def test_app_creation():
    """Тест создания FastAPI приложения"""
    try:
        from app.main import app
        print(f"✅ FastAPI приложение создано: {app.title}")
        return True
    except Exception as e:
        print(f"❌ Ошибка создания приложения: {e}")
        return False

def main():
    print("🚀 Запуск ручных тестов TimeSheet AI")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_routes,
        test_app_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Результат: {passed}/{total} тестов прошли успешно")
    
    if passed == total:
        print("🎉 Все тесты прошли! Базовая архитектура работает корректно.")
        return True
    else:
        print("⚠️  Некоторые тесты не прошли. Требуется дополнительная настройка.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
