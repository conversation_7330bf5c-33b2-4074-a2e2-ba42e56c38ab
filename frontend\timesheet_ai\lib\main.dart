import 'package:flutter/material.dart';
import 'core/http_client.dart';
import 'features/system/health_repository.dart';
import 'features/auth/login_page.dart';
import 'features/auth/register_page.dart';
import 'features/auth/token_store.dart';

void main() {
  runApp(const App());
}

class App extends StatefulWidget {
  const App({super.key});
  @override
  State<App> createState() => _AppState();
}

class _AppState extends State<App> {
  final client = HttpClient('http://10.0.2.2:8000');
  bool? healthy;
  String? role;

  @override
  void initState() {
    super.initState();
    final repo = HealthRepository(client.dio);
    repo.isHealthy().then((ok) => setState(() => healthy = ok));
  }

  void _onLoggedIn(String r) {
    setState(() {
      role = r;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (healthy == null) {
      return const MaterialApp(home: Scaffold(body: Center(child: CircularProgressIndicator())));
    }
    if (!healthy!) {
      return MaterialApp(home: Scaffold(body: Center(child: Text('Backend DOWN'))));
    }
    if (role != null) {
      return MaterialApp(home: Scaffold(body: Center(child: Text('Role: $role - Home screen placeholder'))));
    }
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(title: const Text('Timesheet AI')),
        body: Center(
          child: Column(mainAxisSize: MainAxisSize.min, children: [
            ElevatedButton(onPressed: () => Navigator.of(context).push(MaterialPageRoute(builder: (_) => RegisterPage(client: client))), child: const Text('Register')),
            ElevatedButton(onPressed: () => Navigator.of(context).push(MaterialPageRoute(builder: (_) => LoginPage(client: client, onLoggedIn: _onLoggedIn))), child: const Text('Login')),
          ]),
        ),
      ),
    );
  }
}