# Timesheet AI — архитектура (MVP Sprint1)

## Компоненты:
- backend: FastAPI (uvicorn), PostgreSQL, Alembic
- frontend: Flutter
- CI: GitHub Actions (pre-commit, pytest)
- Запуск: docker compose up --build

## Структура проекта:

```
TimeSheetAI/
├── backend/                 # FastAPI приложение
│   ├── app/                 # Основной код приложения
│   │   ├── api/v1/         # API эндпоинты
│   │   ├── core/           # Конфигурация
│   │   ├── db/             # База данных
│   │   ├── models/         # SQLAlchemy модели
│   │   ├── schemas/        # Pydantic схемы
│   │   └── services/       # Бизнес-логика
│   ├── tests/              # Тесты
│   ├── alembic/            # Миграции БД
│   ├── requirements.txt    # Python зависимости
│   └── Dockerfile          # Docker образ
├── frontend/               # Flutter приложение
│   └── timesheet_ai/       # Мобильное приложение
├── docs/                   # Документация
└── docker-compose.yml      # Конфигурация контейнеров
```

## База данных:

### Таблица users:
- id (primary key)
- email (unique, indexed)
- hashed_password
- role (worker|director)

## API эндпоинты:

- GET / - информация о приложении
- GET /api/v1/health - проверка состояния

## Технологии:

- **Backend**: Python 3.11, FastAPI, SQLAlchemy, PostgreSQL
- **Frontend**: Flutter, Dart
- **DevOps**: Docker, GitHub Actions
- **Тестирование**: pytest, httpx
