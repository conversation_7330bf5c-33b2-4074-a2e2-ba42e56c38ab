# Подробный план разработки TimeSheet AI в VSCode с использованием AI-agent Augment

## Подготовительный этап: Настройка среды разработки (Неделя 0)

### Задача 0.1: Установка и настройка VSCode
```
1. Установить VSCode с официального сайта
2. Установить обязательные расширения:
   - Python
   - Flutter
   - Vue.js
   - Docker
   - PostgreSQL
   - GitLens
   - Thunder Client (для тестирования API)
   - AI-agent Augment
3. Настроить параметры VSCode (settings.json):
   - "editor.formatOnSave": true
   - "python.linting.enabled": true
   - "files.autoSave": "afterDelay"
```

### Задача 0.2: Установка необходимого ПО
```
1. Установить Python 3.11+
2. Установить Node.js 18+
3. Установить Flutter SDK
4. Установить Docker Desktop
5. Установить PostgreSQL 14+
6. Установить Git
7. Проверить версии всех установленных компонентов
```

### Задача 0.3: Настройка AI-agent Augment
```
1. Установить расширение Augment в VSCode
2. Настроить конфигурацию для работы с проектом:
   - Указать основной язык: русский
   - Настроить шаблоны кода согласно руководству проекта
   - Определить правила именования
   - Настроить интеграцию с системой контроля версий
```

## Этап 1: Создание структуры проекта и документации (Неделя 1)

### Задача 1.1: Инициализация репозитория
```
1. Создать новую папку проекта: timesheet-ai
2. Инициализировать Git репозиторий: git init
3. Создать структуру папок согласно руководству:
   - backend/
   - frontend/mobile/
   - frontend/web/
   - docs/
   - infrastructure/
   - scripts/
4. Создать базовые файлы:
   - README.md с описанием проекта
   - .gitignore для всех технологий
   - LICENSE файл
   - CHANGELOG.md
```

### Задача 1.2: Настройка окружения разработки
```
1. Создать docker-compose.yml для разработки:
   - PostgreSQL контейнер
   - Redis контейнер
   - Настроить volumes для персистентности
2. Создать .env файлы для каждого окружения:
   - .env.development
   - .env.staging  
   - .env.production
3. Написать скрипты для инициализации БД:
   - scripts/init-db.sh
   - scripts/seed-data.sh
```

### Задача 1.3: Создание первоначальной документации
```
1. Создать docs/ARCHITECTURE.md с диаграммами системы
2. Создать docs/API_GUIDE.md с описанием эндпоинтов
3. Создать docs/DEVELOPMENT_SETUP.md с инструкциями запуска
4. Создать docs/CODING_STANDARDS.md с правилами кодирования
5. Создать docs/DEPLOYMENT.md с инструкциями развертывания
```

## Этап 2: Разработка Backend API (Недели 2-4)

### Задача 2.1: Настройка базового FastAPI приложения
```
1. Создать backend/requirements.txt с зависимостями:
   - fastapi
   - sqlalchemy
   - psycopg2
   - redis
   - celery
   - openai
   - другие необходимые библиотеки

2. Создать структуру backend/app:
   - main.py - основной файл приложения
   - core/ - ядро приложения (config, database, auth)
   - models/ - SQLAlchemy модели
   - schemas/ - Pydantic схемы
   - services/ - бизнес-логика
   - api/ - эндпоинты
   - utils/ - вспомогательные функции

3. Настроить подключение к БД через SQLAlchemy
4. Реализовать базовую аутентификацию (JWT токены)
```

### Задача 2.2: Реализация основных моделей БД
```
1. Создать модели согласно архитектуре:
   - User (id, email, name, role, company_id, etc.)
   - Company (id, name, settings, created_at)
   - Project (id, name, address, company_id, status)
   - WorkEntry (id, user_id, project_id, date, description, etc.)
   - Timesheet (id, user_id, period_start, period_end, status)
   - Approval (id, timesheet_id, contractor_email, approved_at)

2. Создать миграции с помощью Alembic
3. Написать тестовые данные для заполнения БД
```

### Задача 2.3: Реализация AI-сервиса для парсинга
```
1. Создать сервис для работы с OpenAI API:
   - Настроить подключение с API ключом
   - Реализовать функцию parse_work_entry(text)
   - Обработать возможные ошибки API
   - Добавить кэширование запросов

2. Создать промпт для извлечения структурированных данных:
   - Дата (распознавать "вчера", "сегодня", названия дней)
   - Описание работы
   - Количество и единицы измерения
   - Тип работы (часовая/сдельная)

3. Реализовать fallback-логику при недоступности AI
```

### Задача 2.4: Реализация основных эндпоинтов API
```
1. Аутентификация:
   - POST /api/auth/register
   - POST /api/auth/login  
   - POST /api/auth/refresh
   - GET /api/auth/me

2. Работы:
   - POST /api/work-entries/ - добавление работы
   - GET /api/work-entries/ - список работ с фильтрами
   - PUT /api/work-entries/{id} - редактирование
   - DELETE /api/work-entries/{id} - удаление

3. Проекты:
   - GET /api/projects/ - список проектов
   - POST /api/projects/ - создание проекта
   - PUT /api/projects/{id} - обновление

4. Табели:
   - POST /api/timesheets/generate - генерация табеля
   - GET /api/timesheets/ - список табелей
   - POST /api/timesheets/{id}/approve - подписание

5. Добавить авторизацию ко всем эндпоинтам
```

### Задача 2.5: Реализация фоновых задач с Celery
```
1. Настроить Celery для фоновых задач
2. Реализовать задачи:
   - Генерация отчетов в фоне
   - Отправка email уведомлений
   - Обработка массовых операций
   - Очистка временных файлов
```

### Задача 2.6: Тестирование Backend
```
1. Написать unit-тесты для критичной логики
2. Написать интеграционные тесты для API
3. Настроить pytest с покрытием кода
4. Провести нагрузочное тестирование
```

## Этап 3: Разработка мобильного приложения (Недели 5-8)

### Задача 3.1: Настройка Flutter проекта
```
1. Создать новый Flutter проект в frontend/mobile/
2. Настроить структуру папок согласно руководству:
   - lib/
     - core/ - константы, ошибки, утилиты
     - data/ - модели, репозитории
     - domain/ - бизнес-логика
     - presentation/ - экраны, виджеты
     - main.dart - входная точка

3. Добавить необходимые зависимости в pubspec.yaml:
   - http для API запросов
   - provider для стейт-менеджмента
   - shared_preferences для локального хранилища
   - intl для интернационализации
   - flutter_chat_ui для интерфейса чата
```

### Задача 3.2: Реализация системы аутентификации
```
1. Создать экран входа/регистрации
2. Реализовать сохранение JWT токена
3. Добавить автоматическое обновление токена
4. Реализовать выход из системы
```

### Задача 3.3: Реализация главного экрана рабочего
```
1. Создать WorkerHomeScreen с:
   - Кнопкой быстрого добавления работы
   - Списком последних записей
   - Статистикой за период
   - Переключением активного проекта

2. Реализовать быстрые действия:
   - "Вчера" - предзаполнение даты
   - "Сегодня" - предзаполнение даты
   - Частые операции (8 часов, полный день)
```

### Задача 3.4: Реализация чат-интерфейса для ввода работ
```
1. Создать экран AddWorkScreen с интерфейсом чата
2. Реализовать отправку текста на бекенд для парсинга
3. Добавить индикатор "ИИ обрабатывает..."
4. Показывать распознанные данные для подтверждения
5. Реализовать кнопки "Сохранить" и "Исправить"
```

### Задача 3.5: Реализация списка и редактирования работ
```
1. Создать WorkListScreen с фильтрами по дате
2. Реализовать возможность редактирования записей
3. Добавить удаление записей свайпом
4. Реализовать группировку по дням/неделям
```

### Задача 3.6: Реализация функционала для директора
```
1. Создать DirectorHomeScreen с дашбордом
2. Реализовать просмотр рабочих и их активности
3. Добавить генерацию и экспорт отчетов
4. Реализовать управление компанией и проектами
```

### Задача 3.7: Тестирование мобильного приложения
```
1. Написать widget-тесты для основных экранов
2. Протестировать на разных устройствах и iOS/Android
3. Провести usability-тестирование
```

## Этап 4: Разработка веб-панели директора (Недели 9-11)

### Задача 4.1: Настройка Vue.js проекта
```
1. Создать новый Vue.js проект в frontend/web/
2. Настроить структуру папок:
   - src/
     - components/ - переиспользуемые компоненты
     - views/ - страницы приложения
     - router/ - маршрутизация
     - store/ - управление состоянием
     - services/ - API сервисы
     - utils/ - вспомогательные функции

3. Добавить необходимые зависимости:
   - vue-router для маршрутизации
   - pinia для управления состоянием
   - axios для HTTP запросов
   - tailwindcss для стилизации
   - chart.js для графиков
```

### Задача 4.2: Реализация системы аутентификации
```
1. Создать страницу входа
2. Реализовать сохранение и обновление токенов
3. Добавить защищенные маршруты
```

### Задача 4.3: Реализация дашборда директора
```
1. Создать главную страницу с ключевыми метриками:
   - Количество активных рабочих
   - Общее количество часов за период
   - Статус проектов
   - Графики активности

2. Реализовать фильтры по дате и проектам
3. Добавить быстрый доступ к часто используемым функциям
```

### Задача 4.4: Реализация управления рабочими
```
1. Создать страницу со списком рабочих
2. Реализовать добавление/удаление рабочих
3. Добавить просмотр детальной информации по каждому рабочему
4. Реализовать экспорт данных по рабочим
```

### Задача 4.5: Реализация системы отчетов
```
1. Создать страницу генерации отчетов
2. Реализовать различные форматы отчетов:
   - По рабочим
   - По проектам
   - Сводные отчеты

3. Добавить экспорт в Excel, PDF, CSV
4. Реализовать планирование автоматической отправки отчетов
```

### Задача 4.6: Реализация управления проектами
```
1. Создать страницу управления проектами
2. Реализовать CRUD операции для проектов
3. Добавить назначение рабочих на проекты
4. Реализовать отслеживание статуса проектов
```

## Этап 5: Реализация системы подписания для генподрядчиков (Неделя 12)

### Задача 5.1: Реализация генерации уникальных ссылок
```
1. Создать модель для хранения токенов подписания
2. Реализовать генерацию уникальных ссылок с ограничением времени
3. Добавить отслеживание статуса подписания
```

### Задача 5.2: Создание веб-страницы для подписания
```
1. Создать отдельное простое Vue.js приложение для подписания
2. Реализовать красивый и понятный просмотр табеля
3. Добавить кнопку подтверждения
4. Реализовать обработку подписания и уведомления
```

### Задача 5.3: Реализация email уведомлений
```
1. Настроить систему отправки email
2. Реализовать шаблоны писем для подписания
3. Добавить уведомления о неподписанных табелях
4. Реализовать напоминания
```

## Этап 6: Интеграция и тестирование (Недели 13-14)

### Задача 6.1: Интеграция всех компонентов
```
1. Настроить взаимодействие между всеми компонентами системы
2. Реализовать единую систему аутентификации
3. Настроить CORS для веб-приложений
4. Проверить работу всех интеграций
```

### Задача 6.2: Полное тестирование системы
```
1. Провести end-to-end тестирование всех сценариев
2. Протестировать производительность под нагрузкой
3. Провести security audit системы
4. Исправить обнаруженные ошибки
```

### Задача 6.3: Подготовка к продакшену
```
1. Настроить окружение для production
2. Настроить мониторинг и логирование
3. Подготовить документацию для развертывания
4. Настроить CI/CD пайплайн
```

## Этап 7: Развертывание и запуск (Неделя 15)

### Задача 7.1: Развертывание на продакшен сервере
```
1. Настроить сервер (VPS или облачный провайдер)
2. Развернуть все компоненты системы
3. Настроить доменные имена и SSL сертификаты
4. Настроить бэкапы и мониторинг
```

### Задача 7.2: Финальное тестирование
```
1. Провести финальное тестирование на продакшен окружении
2. Проверить все функциональные требования
3. Убедиться в отсутствии критических ошибок
```

### Задача 7.3: Запуск и поддержка
```
1. Официальный запуск системы
2. Мониторинг работы в реальном времени
3. Готовность к быстрому исправлению возможных проблем
4. Сбор обратной связи от пользователей
```

## Дополнительные рекомендации для работы с AI-agent Augment

### Для эффективной работы:
```
1. Всегда давайте агенту полный контекст задачи
2. Используйте четкие и конкретные формулировки
3. Разбивайте большие задачи на мелкие подзадачи
4. Проверяйте сгенерированный код на соответствие стандартам
5. Тестируйте каждый созданный компонент
6. Документируйте все принятые архитектурные решения
```

### Пример запроса к AI-agent:
```
"Создай модель User для SQLAlchemy со следующими полями:
- id: integer, primary key
- email: string, unique, not null
- name: string, not null  
- role: string (worker/director)
- company_id: foreign key to companies
- created_at: datetime

Добавь методы:
- create_user: создание нового пользователя
- get_by_email: поиск по email
- get_by_company: получение всех пользователей компании

Следуй стандартам кодирования из документации проекта."
```

Этот план обеспечивает полное пошаговое руководство для создания проекта с нуля, с учетом всех технических и бизнес-требований, описанных в предоставленных документах.