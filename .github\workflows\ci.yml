name: CI

on:
  push:
    branches: [ "sprint-1", "main" ]
  pull_request:
    branches: [ "sprint-1", "main" ]

jobs:
  backend-checks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install deps
        run: |
          python -m pip install --upgrade pip
          pip install pre-commit pytest pytest-asyncio httpx
      - name: Run pre-commit
        run: |
          pre-commit run --all-files || true
      - name: Run tests
        run: |
          pytest -q backend/tests
