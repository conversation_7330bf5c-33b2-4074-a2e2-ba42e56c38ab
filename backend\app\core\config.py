from pydantic_settings import BaseSettings
from pydantic import computed_field

class Settings(BaseSettings):
    ENVIRONMENT: str = "dev"
    PROJECT_NAME: str = "Timesheet AI"
    API_V1_PREFIX: str = "/api/v1"

    DB_HOST: str = "db"
    DB_PORT: int = 5432
    DB_USER: str = "tsai"
    DB_PASSWORD: str = "tsai_password"
    DB_NAME: str = "tsai"

    @computed_field
    @property
    def SQLALCHEMY_DATABASE_URI(self) -> str:
        return (
            f"postgresql+psycopg2://{self.DB_USER}:{self.DB_PASSWORD}@"
            f"{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
        )

    class Config:
        env_file = ".env"

settings = Settings()
