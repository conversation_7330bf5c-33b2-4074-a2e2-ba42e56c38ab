import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import '../../core/http_client.dart';

class RegisterPage extends StatefulWidget {
  final HttpClient client;
  const RegisterPage({super.key, required this.client});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  String role = 'worker';

  bool loading = false;
  String? msg;

  Future<void> _register() async {
    setState(() { loading = true; msg = null; });
    try {
      final r = await widget.client.dio.post('/api/v1/auth/register', data: {
        'email': emailController.text,
        'password': passwordController.text,
        'role': role,
      });
      if (r.statusCode == 201) {
        setState(() { msg = 'Registered'; });
      } else {
        setState(() { msg = 'Error'; });
      }
    } catch (e) {
      setState(() { msg = e.toString(); });
    } finally {
      setState(() { loading = false; });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Register')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(children: [
          TextField(controller: emailController, decoration: const InputDecoration(labelText: 'Email')),
          TextField(controller: passwordController, decoration: const InputDecoration(labelText: 'Password'), obscureText: true),
          DropdownButton<String>(
            value: role,
            items: const [
              DropdownMenuItem(value: 'worker', child: Text('Worker')),
              DropdownMenuItem(value: 'director', child: Text('Director')),
            ],
            onChanged: (v) => setState(() { role = v ?? 'worker'; }),
          ),
          ElevatedButton(onPressed: loading ? null : _register, child: Text(loading ? '...' : 'Register')),
          if (msg != null) Text(msg!),
        ]),
      ),
    );
  }
}
