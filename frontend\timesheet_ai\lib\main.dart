import 'package:flutter/material.dart';
import 'core/http_client.dart';
import 'features/system/health_repository.dart';

void main() {
  runApp(const App());
}

class App extends StatefulWidget {
  const App({super.key});
  @override
  State<App> createState() => _AppState();
}

class _AppState extends State<App> {
  late final HealthRepository repo;
  bool? healthy;

  @override
  void initState() {
    super.initState();
    final client = HttpClient('http://10.0.2.2:8000'); // эмулятор Android
    repo = HealthRepository(client.dio);
    _check();
  }

  Future<void> _check() async {
    try {
      final ok = await repo.isHealthy();
      setState(() => healthy = ok);
    } catch (e) {
      setState(() => healthy = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Timesheet AI',
      home: Scaffold(
        appBar: AppBar(title: const Text('Timesheet AI')),
        body: Center(
          child: healthy == null
              ? const CircularProgressIndicator()
              : Text(healthy! ? 'Backend OK' : 'Backend DOWN'),
        ),
      ),
    );
  }
}