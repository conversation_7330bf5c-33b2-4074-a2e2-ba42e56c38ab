# Спринт 2 — «Авторизация и роли» — подробное ТЗ для ИИ-агента (чётко, шаг-за-шагом)

Ниже — **однозначное, детализированное техническое задание**, составленное так, чтобы ИИ-агент выполнил инструкции строго и без «творчества».
Перед началом убедись, что **Спринт 1 завершён** (в репозитории есть ветка `sprint-1`, `docker compose up --build` поднимает `db` и `backend`, `/api/v1/health` работает). Всё, что делаем в Спринте 2, базируется на этом.

**Жёсткие правила для агента**

* Не добавлять никаких библиотек, файлов, полей таблиц или эндпоинтов, которые **не указаны** в этом ТЗ.
* Выполнять команды и создавать файлы **точно** как в тексте (имена файлов/путей, содержание).
* Все коммиты — с префиксом `sprint2:`.
* Если что-то не запускается — фиксировать проблему как Issue (см. пункт «Issues»), не «додумывать» решение.

---

## Цели спринта

1. Реализовать регистрацию и вход пользователей через JSON API.
2. Реализовать JWT-аутентификацию (access token).
3. Реализовать роль пользователя (`worker` | `director`) и проверку текущего пользователя по токену.
4. Добавить минимальные тесты (регистрация → логин → получение /auth/me).
5. Обновить документацию и создать соответствующие Issues/PR-чеклисты.

---

## Общая архитектура (кратко)

* Backend — FastAPI (в контейнере Docker).
* Хранение секретов — в `backend/.env` (новые переменные добавляем туда).
* Frontend (Flutter) — добавляем страницы регистрации/логина и редирект по роли.
* Для хеширования паролей — библиотека `passlib[bcrypt]`. Для jwt — `python-jose[cryptography]`. (Добавляем в `backend/requirements.txt` — точно см. ниже.)

---

## Шаги (выполнять последовательно). Команды — точные.

### 0. Создать рабочую ветку

```bash
git checkout -b sprint-2
```

---

### 1. Обновить зависимости backend

Открой `backend/requirements.txt` и **добавь** (строго как указано — версии указаны):

```
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
python-multipart==0.0.6
```

> Примечание: `python-multipart` не обязателен для JSON, но часто нужен при работе с OAuth2; добавляем безопасно.

Сохранить и закоммитить:

```bash
git add backend/requirements.txt
git commit -m "sprint2: add security dependencies to requirements"
```

После изменения зависимостей — **пересобрать контейнер backend**:

```bash
docker compose up --build -d
```

(Если контейнер уже работает — останови и запусти заново, чтобы pip установил новые пакеты.)

---

### 2. Добавить секреты и конфиг для токенов

Открой `backend/.env.example` и **добавь** в конец следующие строки (точно):

```
SECRET_KEY=replace_this_with_a_random_secure_value
ACCESS_TOKEN_EXPIRE_MINUTES=60
ALGORITHM=HS256
```

Скопируй `backend/.env.example` в `backend/.env` локально и замени `replace_this_with_a_random_secure_value` на любую случайную строку (на проде — секретные ключи хранить в безопасном месте).

Commit:

```bash
git add backend/.env.example
git commit -m "sprint2: add JWT config to .env.example"
```

> Не коммитить `backend/.env` в репозиторий.

---

### 3. Добавить модуль безопасности (password + jwt)

Создать файл `backend/app/core/security.py` со следующим содержимым **точно**:

```python
# backend/app/core/security.py

from datetime import datetime, timedelta
from typing import Optional

from passlib.context import CryptContext
from jose import jwt

from app.core.config import settings

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

ALGORITHM = settings.ALGORITHM
SECRET_KEY = settings.SECRET_KEY
ACCESS_TOKEN_EXPIRE_MINUTES = int(settings.ACCESS_TOKEN_EXPIRE_MINUTES)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def create_access_token(subject: str, expires_delta: Optional[timedelta] = None, extra_claims: dict | None = None) -> str:
    to_encode = {"sub": str(subject)}
    if extra_claims:
        to_encode.update(extra_claims)
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt
```

Commit:

```bash
git add backend/app/core/security.py
git commit -m "sprint2: add security module for hashing and JWT"
```

---

### 4. Добавить Pydantic-схемы для авторизации и пользователя

Создать файл `backend/app/schemas/user.py` (точно):

```python
# backend/app/schemas/user.py

from pydantic import BaseModel, EmailStr, Field
from typing import Optional

class UserBase(BaseModel):
    email: EmailStr

class UserCreate(UserBase):
    password: str = Field(..., min_length=6)
    role: Optional[str] = "worker"  # "worker" or "director"

class UserOut(UserBase):
    id: int
    role: str

    class Config:
        orm_mode = True

class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"

class TokenData(BaseModel):
    sub: Optional[str] = None
    role: Optional[str] = None
```

Commit:

```bash
git add backend/app/schemas/user.py
git commit -m "sprint2: add Pydantic schemas for user and token"
```

---

### 5. Добавить CRUD-функции для пользователей

Создать папку `backend/app/crud` если её нет, и файл `backend/app/crud/crud_user.py` с содержимым **точно**:

```python
# backend/app/crud/crud_user.py

from sqlalchemy.orm import Session
from app.models.user import User
from app.schemas.user import UserCreate
from app.core.security import get_password_hash

def get_user_by_email(db: Session, email: str):
    return db.query(User).filter(User.email == email).first()

def create_user(db: Session, user_in: UserCreate):
    hashed_password = get_password_hash(user_in.password)
    db_user = User(email=user_in.email, hashed_password=hashed_password, role=user_in.role)
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user
```

Commit:

```bash
git add backend/app/crud/crud_user.py
git commit -m "sprint2: add crud_user with create and get_by_email"
```

---

### 6. Добавить зависимости и функции для получения текущего пользователя

Создать файл `backend/app/api/v1/deps.py` со следующим содержимым **точно**:

```python
# backend/app/api/v1/deps.py

from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from sqlalchemy.orm import Session

from app.core.config import settings
from app.db.session import SessionLocal
from app.models.user import User
from app.schemas.user import TokenData

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_PREFIX}/auth/login")

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)) -> User:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        user_id: str = payload.get("sub")
        role: str = payload.get("role")
        if user_id is None:
            raise credentials_exception
        token_data = TokenData(sub=user_id, role=role)
    except JWTError:
        raise credentials_exception
    user = db.query(User).filter(User.id == int(token_data.sub)).first()
    if user is None:
        raise credentials_exception
    return user

def require_roles(user_roles: list[str]):
    def _require(user: User = Depends(get_current_user)):
        if user.role not in user_roles:
            raise HTTPException(status_code=403, detail="Operation not permitted for this role")
        return user
    return _require
```

Commit:

```bash
git add backend/app/api/v1/deps.py
git commit -m "sprint2: add auth deps and current_user dependency"
```

---

### 7. Добавить маршруты авторизации (register, login, me)

Создать файл `backend/app/api/v1/endpoints/auth.py` со следующим **точным** содержимым:

```python
# backend/app/api/v1/endpoints/auth.py

from datetime import timedelta
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.schemas.user import UserCreate, UserOut, Token
from app.crud.crud_user import get_user_by_email, create_user
from app.api.v1.deps import get_db, get_current_user
from app.core.security import verify_password, create_access_token
from app.core.config import settings

router = APIRouter()

@router.post("/register", response_model=UserOut, status_code=status.HTTP_201_CREATED)
def register(user_in: UserCreate, db: Session = Depends(get_db)):
    existing = get_user_by_email(db, user_in.email)
    if existing:
        raise HTTPException(status_code=400, detail="Email already registered")
    user = create_user(db, user_in)
    return user

@router.post("/login", response_model=Token)
def login(form_data: UserCreate, db: Session = Depends(get_db)):
    """
    Вход через JSON: { "email": "", "password": "" }.
    (Мы используем UserCreate для простоты — role игнорируется при логине)
    """
    user = get_user_by_email(db, form_data.email)
    if not user:
        raise HTTPException(status_code=400, detail="Incorrect email or password")
    if not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(status_code=400, detail="Incorrect email or password")

    access_token_expires = timedelta(minutes=int(settings.ACCESS_TOKEN_EXPIRE_MINUTES))
    extra_claims = {"role": user.role}
    access_token = create_access_token(subject=str(user.id), expires_delta=access_token_expires, extra_claims=extra_claims)
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/me", response_model=UserOut)
def read_users_me(current_user = Depends(get_current_user)):
    return current_user
```

> Примечание: для простоты вход принимает JSON c полями `email` и `password`. Мы использовали `UserCreate` schema в качестве входной модели (role игнорируется при логине).

Commit:

```bash
git add backend/app/api/v1/endpoints/auth.py
git commit -m "sprint2: add auth endpoints register/login/me"
```

---

### 8. Подключить роуты в main.py

Открой `backend/app/main.py` и **вставь** импорт и подключение нового роутера (если уже есть api router, добавь отдельный include):

Нужно либо модифицировать существующий `api_router` либо прямо подключить:

```python
# в backend/app/main.py (вверху уже есть)
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.core.config import settings
from app.api.v1.routes import router as api_router
from app.api.v1.endpoints import auth as auth_router  # добавь этот импорт

app = FastAPI(title=settings.PROJECT_NAME)

# ... middleware ...

# основной роутер
app.include_router(api_router, prefix=settings.API_V1_PREFIX)

# подключаем auth роутер
app.include_router(auth_router.router, prefix=f"{settings.API_V1_PREFIX}/auth", tags=["auth"])
```

Если файл `app/api/v1/__init__.py` отсутствует — не нужно его создавать. Просто убедись, что импорт корректен.

Commit:

```bash
git add backend/app/main.py
git commit -m "sprint2: include auth router in main"
```

---

### 9. Обновить Alembic при необходимости

**Важно:** в этом спринте мы **не меняем** модель `User` (структура таблицы остаётся такой, как в Спринте 1). Поэтому **новых миграций не требуется**, *если* ты не менял/добавлял колонки в `app/models/user.py`.

Если ты изменял модель — делай миграцию так:

```bash
cd backend
alembic revision -m "update users model" --autogenerate
alembic upgrade head
```

(И коммит соответствующих файлов.)

---

### 10. Тесты backend — регистрация и логин

Создай тест `backend/tests/test_auth.py` со следующим содержимым:

```python
# backend/tests/test_auth.py

import pytest
import httpx
import uuid

BASE = "http://localhost:8000"

@pytest.mark.asyncio
async def test_register_and_login():
    unique = uuid.uuid4().hex[:8]
    email = f"test_{unique}@example.com"
    password = "password123"

    async with httpx.AsyncClient() as client:
        # register
        r = await client.post(f"{BASE}/api/v1/auth/register", json={"email": email, "password": password, "role": "worker"})
        assert r.status_code == 201
        data = r.json()
        assert data["email"] == email
        assert data["role"] == "worker"

        # login
        r2 = await client.post(f"{BASE}/api/v1/auth/login", json={"email": email, "password": password})
        assert r2.status_code == 200
        token = r2.json().get("access_token")
        assert token is not None

        # me
        headers = {"Authorization": f"Bearer {token}"}
        r3 = await client.get(f"{BASE}/api/v1/auth/me", headers=headers)
        assert r3.status_code == 200
        assert r3.json()["email"] == email
```

Commit:

```bash
git add backend/tests/test_auth.py
git commit -m "sprint2: add tests for register/login/me"
```

**Запуск тестов локально (требуется поднятый backend):**

```bash
docker compose up --build -d
pytest -q backend/tests/test_auth.py
```

---

### 11. Frontend — простая реализация экранов регистрации/логина и роль-редирект

Мы добавим минимальные страницы на Flutter: `lib/features/auth/register_page.dart`, `lib/features/auth/login_page.dart`, и небольшой механизм хранения токена в памяти.

**1)** Создай файл `lib/features/auth/token_store.dart`:

```dart
// lib/features/auth/token_store.dart
class TokenStore {
  static String? accessToken;
}
```

**2)** `lib/features/auth/register_page.dart` (точно):

```dart
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import '../core/http_client.dart';

class RegisterPage extends StatefulWidget {
  final HttpClient client;
  const RegisterPage({super.key, required this.client});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  String role = 'worker';

  bool loading = false;
  String? msg;

  Future<void> _register() async {
    setState(() { loading = true; msg = null; });
    try {
      final r = await widget.client.dio.post('/api/v1/auth/register', data: {
        'email': emailController.text,
        'password': passwordController.text,
        'role': role,
      });
      if (r.statusCode == 201) {
        setState(() { msg = 'Registered'; });
      } else {
        setState(() { msg = 'Error'; });
      }
    } catch (e) {
      setState(() { msg = e.toString(); });
    } finally {
      setState(() { loading = false; });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Register')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(children: [
          TextField(controller: emailController, decoration: const InputDecoration(labelText: 'Email')),
          TextField(controller: passwordController, decoration: const InputDecoration(labelText: 'Password'), obscureText: true),
          DropdownButton<String>(
            value: role,
            items: const [
              DropdownMenuItem(value: 'worker', child: Text('Worker')),
              DropdownMenuItem(value: 'director', child: Text('Director')),
            ],
            onChanged: (v) => setState(() { role = v ?? 'worker'; }),
          ),
          ElevatedButton(onPressed: loading ? null : _register, child: Text(loading ? '...' : 'Register')),
          if (msg != null) Text(msg!),
        ]),
      ),
    );
  }
}
```

**3)** `lib/features/auth/login_page.dart` (точно):

```dart
import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import '../core/http_client.dart';
import 'token_store.dart';

class LoginPage extends StatefulWidget {
  final HttpClient client;
  final void Function(String role) onLoggedIn;
  const LoginPage({super.key, required this.client, required this.onLoggedIn});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  bool loading = false;
  String? msg;

  Future<void> _login() async {
    setState(() { loading = true; msg = null; });
    try {
      final r = await widget.client.dio.post('/api/v1/auth/login', data: {
        'email': emailController.text,
        'password': passwordController.text,
      });
      final token = r.data['access_token'];
      TokenStore.accessToken = token;
      // get current user
      final r2 = await widget.client.dio.get('/api/v1/auth/me', options: Options(headers: {
        'Authorization': 'Bearer $token',
      }));
      final role = r2.data['role'] as String;
      widget.onLoggedIn(role);
    } catch (e) {
      setState(() { msg = e.toString(); });
    } finally {
      setState(() { loading = false; });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Login')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(children: [
          TextField(controller: emailController, decoration: const InputDecoration(labelText: 'Email')),
          TextField(controller: passwordController, decoration: const InputDecoration(labelText: 'Password'), obscureText: true),
          ElevatedButton(onPressed: loading ? null : _login, child: Text(loading ? '...' : 'Login')),
          if (msg != null) Text(msg!),
        ]),
      ),
    );
  }
}
```

**4)** Обнови `lib/main.dart` чтобы иметь роутинг на Login/Register и редирект по роли. Пример:

```dart
// lib/main.dart (фрагмент — обнови существующий main)
import 'package:flutter/material.dart';
import 'core/http_client.dart';
import 'features/system/health_repository.dart';
import 'features/auth/login_page.dart';
import 'features/auth/register_page.dart';
import 'features/auth/token_store.dart';

void main() {
  runApp(const App());
}

class App extends StatefulWidget {
  const App({super.key});
  @override
  State<App> createState() => _AppState();
}

class _AppState extends State<App> {
  final client = HttpClient('http://10.0.2.2:8000');
  bool? healthy;
  String? role;

  @override
  void initState() {
    super.initState();
    final repo = HealthRepository(client.dio);
    repo.isHealthy().then((ok) => setState(() => healthy = ok));
  }

  void _onLoggedIn(String r) {
    setState(() {
      role = r;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (healthy == null) {
      return const MaterialApp(home: Scaffold(body: Center(child: CircularProgressIndicator())));
    }
    if (!healthy!) {
      return MaterialApp(home: Scaffold(body: Center(child: Text('Backend DOWN'))));
    }
    if (role != null) {
      return MaterialApp(home: Scaffold(body: Center(child: Text('Role: $role - Home screen placeholder'))));
    }
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(title: const Text('Timesheet AI')),
        body: Center(
          child: Column(mainAxisSize: MainAxisSize.min, children: [
            ElevatedButton(onPressed: () => Navigator.of(context).push(MaterialPageRoute(builder: (_) => RegisterPage(client: client))), child: const Text('Register')),
            ElevatedButton(onPressed: () => Navigator.of(context).push(MaterialPageRoute(builder: (_) => LoginPage(client: client, onLoggedIn: _onLoggedIn))), child: const Text('Login')),
          ]),
        ),
      ),
    );
  }
}
```

Commit frontend изменения:

```bash
git add frontend/timesheet_ai/lib
git commit -m "sprint2: add auth pages (register/login) and token store in Flutter"
```

---

### 12. Документация и Issues

Создать Issues (если используешь GitHub) с этими названиями (по одной задаче на issue):

* `sprint2/1 — add JWT settings to .env.example`
* `sprint2/2 — add security module (hash and JWT)`
* `sprint2/3 — add user schemas and CRUD`
* `sprint2/4 — add auth endpoints (register/login/me)`
* `sprint2/5 — add tests for auth`
* `sprint2/6 — add frontend auth pages (register/login)`

В описании каждого issue — вставить соответствующую часть этого ТЗ (копипаста). PR привязывать к issue.

Commit docs:

```bash
git add docs
git commit -m "sprint2: update docs and add sprint2 issues checklist"
```

---

## Acceptance Criteria — что считать Done (Definition of Done)

Спринт 2 считается завершённым, если выполнены все пункты:

1. В репозитории есть ветка `sprint-2` с коммитами, префикс `sprint2:` у всех коммитов.
2. В `backend/requirements.txt` добавлены: `passlib[bcrypt]`, `python-jose[cryptography]`, `python-multipart`.
3. В `backend/.env.example` добавлены `SECRET_KEY`, `ACCESS_TOKEN_EXPIRE_MINUTES`, `ALGORITHM`.
4. В `backend/app/core/security.py` реализованы `get_password_hash`, `verify_password`, `create_access_token`.
5. Реализованы Pydantic-схемы в `backend/app/schemas/user.py`.
6. CRUD функции: `get_user_by_email`, `create_user` в `backend/app/crud/crud_user.py`.
7. Депенденси `get_current_user` и `require_roles` в `backend/app/api/v1/deps.py` работают и проверяют токен.
8. Эндпоинты зарегистрированы:

   * `POST /api/v1/auth/register` — регистрация (возвращает `UserOut`).
   * `POST /api/v1/auth/login` — возвращает `{"access_token": "...", "token_type": "bearer"}`.
   * `GET /api/v1/auth/me` — возвращает текущего пользователя (по токену).
9. Тест `backend/tests/test_auth.py` проходит локально при поднятом `docker compose up --build`.
10. На фронтенде доступны страницы `Register` и `Login`; при успешном логине приложение делает `GET /api/v1/auth/me` и получает роль, после чего происходит редирект на заглушку домашнего экрана с отображением роли.
11. Все новые файлы закоммичены и PR привязан к соответствующему issue.

---

## Методы тестирования (пошагово)

### Локальный порядок тестирования (обязателен)

1. Поднять сервисы:

```bash
docker compose up --build -d
```

2. Проверить health:

```bash
curl http://localhost:8000/api/v1/health
# ожидаемый ответ: {"status":"ok"}
```

3. Пробная регистрация:

```bash
curl -X POST "http://localhost:8000/api/v1/auth/register" -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"pass12345","role":"worker"}'
# ожидаемый статус 201, JSON с полем email и role
```

4. Пробный логин:

```bash
curl -X POST "http://localhost:8000/api/v1/auth/login" -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"pass12345"}'
# ожидаемый JSON: {"access_token":"...", "token_type":"bearer"}
```

5. Проверка /me:

```bash
TOKEN=... # из предыдущего шага
curl -H "Authorization: Bearer $TOKEN" http://localhost:8000/api/v1/auth/me
# ожидаемый JSON с полями id, email, role
```

6. Запуск автотестов:

```bash
pytest -q backend/tests/test_auth.py
# ожидается: 1 passed
```

### Ручное тестирование фронтенда

1. Запустить Flutter (в Android эмуляторе):

```bash
cd frontend/timesheet_ai
flutter pub get
flutter run
```

2. На UI: нажать Register → заполнить email/password → получить сообщение об успехе.
3. Нажать Login → ввести те же данные → при успехе приложение должно показать домашнюю заглушку с ролью.

---

## Замечания и ограничения (жёсткие)

* Не внедрять двухфакторную аутентификацию, refresh tokens или OAuth с внешними провайдерами — эти опции **вне** этого спринта.
* Не менять структуру таблицы `users` без явного указания в ТЗ.
* Все изменения оформлять отдельными коммитами с префиксом `sprint2:` и привязывать PR к соответствующим Issues.

---
