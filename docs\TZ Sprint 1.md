# Инструкция для ИИ-агента — Sprint 1 (выполнить по шагам)

---

## 0. Предварительные условия (что уже установлено на машине исполнителя)

* Git установлен.
r
* Python 3.11+ установлен (локально для тестов, но контейнеры используются для запуска).
* Flutter SDK установлен (для запуска фронтенда локально или на CI если требуется).
* Рабочая ветка: создавай ветку `sprint-1` и работай в ней.

Команда для создания ветки:

```bash
git checkout -b sprint-1
```

---

## 1. Создать структуру репозитория и базовые файлы

Выполнить (в корне проекта):

```bash
mkdir -p backend/app/api/v1 backend/app/core backend/app/db backend/app/models backend/app/schemas backend/app/services backend/tests
mkdir -p frontend
mkdir -p docs
touch README.md
```

Создать файл `.gitignore` с точным содержимым:

```
# Python
__pycache__/
*.pyc
.venv/
env/
# Alembic
backend/alembic/
# Env
*.env
backend/.env
# IDE
.vscode/
.idea/
# Flutter
**/build/
**/.dart_tool/
**/.flutter-plugins
**/.packages
**/pubspec.lock
```

Commit:

```bash
git add .
git commit -m "sprint1: init repo structure and .gitignore"
```

---

## 2. Создать Docker Compose (точно как ниже)

Создать файл `docker-compose.yml` в корне со следующим содержимым:

```yaml
version: "3.8"
services:
  db:
    image: postgres:16-alpine
    container_name: tsai_db
    environment:
      POSTGRES_USER: tsai
      POSTGRES_PASSWORD: tsai_password
      POSTGRES_DB: tsai
    ports:
      - "5432:5432"
    volumes:
      - dbdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U tsai -d tsai"]
      interval: 5s
      timeout: 3s
      retries: 10

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: tsai_backend
    env_file:
      - ./backend/.env
    depends_on:
      db:
        condition: service_healthy
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
    command: ["bash", "-lc", "alembic upgrade head && uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"]

volumes:
  dbdata:
```

Commit:

```bash
git add docker-compose.yml
git commit -m "sprint1: add docker-compose with db and backend"
```

---

## 3. Подготовка backend: зависимости и Dockerfile

Создать файл `backend/requirements.txt` (точно):

```
fastapi==0.115.0
uvicorn[standard]==0.30.6
pydantic==2.9.2
pydantic-settings==2.4.0
SQLAlchemy==2.0.35
alembic==1.13.2
psycopg2-binary==2.9.9
python-dotenv==1.0.1
httpx==0.27.2
pre-commit==3.6.0
pytest==7.4.0
pytest-asyncio==0.21.0
```

Создать файл `backend/Dockerfile`:

```dockerfile
FROM python:3.11-slim

WORKDIR /app

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1

RUN apt-get update && apt-get install -y build-essential curl && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000
```

Commit:

```bash
git add backend/requirements.txt backend/Dockerfile
git commit -m "sprint1: backend requirements and Dockerfile"
```

---

## 4. Backend — конфигурация приложения и подключение к БД

Создать файл `backend/.env.example`:

```
ENVIRONMENT=dev
PROJECT_NAME=Timesheet AI
API_V1_PREFIX=/api/v1
DB_HOST=db
DB_PORT=5432
DB_USER=tsai
DB_PASSWORD=tsai_password
DB_NAME=tsai
```

Скопировать в `.env` локально (в репозитории .env не должен быть зафиксирован, но для локального теста допустимо):

```bash
cp backend/.env.example backend/.env
```

Создать файл `backend/app/core/config.py` с содержимым:

```python
from pydantic_settings import BaseSettings
from pydantic import computed_field

class Settings(BaseSettings):
    ENVIRONMENT: str = "dev"
    PROJECT_NAME: str = "Timesheet AI"
    API_V1_PREFIX: str = "/api/v1"

    DB_HOST: str = "db"
    DB_PORT: int = 5432
    DB_USER: str = "tsai"
    DB_PASSWORD: str = "tsai_password"
    DB_NAME: str = "tsai"

    @computed_field
    @property
    def SQLALCHEMY_DATABASE_URI(self) -> str:
        return (
            f"postgresql+psycopg2://{self.DB_USER}:{self.DB_PASSWORD}@"
            f"{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
        )

    class Config:
        env_file = ".env"

settings = Settings()
```

Создать `backend/app/db/session.py`:

```python
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, DeclarativeBase

from app.core.config import settings

engine = create_engine(settings.SQLALCHEMY_DATABASE_URI, pool_pre_ping=True)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

class Base(DeclarativeBase):
    pass
```

Создать `backend/app/models/user.py`:

```python
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy import String
from app.db.session import Base

class User(Base):
    __tablename__ = "users"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    email: Mapped[str] = mapped_column(String(255), unique=True, index=True, nullable=False)
    hashed_password: Mapped[str] = mapped_column(String(255), nullable=False)
    role: Mapped[str] = mapped_column(String(50), nullable=False, default="worker")  # worker|director
```

Создать `backend/app/db/base.py` с импортом модели:

```python
# Импорт моделей для Alembic автогенерации
from app.models.user import User  # noqa
```

Commit все новые файлы:

```bash
git add backend/app
git commit -m "sprint1: backend config, db session and User model"
```

---

## 5. Alembic — инициализация и env.py (точно)

Инициализировать alembic (входя в директорию backend):

```bash
cd backend
alembic init alembic
```

Заменить содержимое `backend/alembic/env.py` на:

```python
import sys
from logging.config import fileConfig
from sqlalchemy import engine_from_config, pool
from alembic import context

from app.core.config import settings
from app.db.session import Base
import app.db.base  # noqa: F401 - импорт моделей

config = context.config
fileConfig(config.config_file_name)

target_metadata = Base.metadata

def run_migrations_offline():
    url = settings.SQLALCHEMY_DATABASE_URI
    context.configure(
        url=url, target_metadata=target_metadata, literal_binds=True, compare_type=True
    )
    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online():
    configuration = config.get_section(config.config_ini_section)
    configuration["sqlalchemy.url"] = settings.SQLALCHEMY_DATABASE_URI
    connectable = engine_from_config(
        configuration, prefix="sqlalchemy.", poolclass=pool.NullPool
    )
    with connectable.connect() as connection:
        context.configure(connection=connection, target_metadata=target_metadata, compare_type=True)
        with context.begin_transaction():
            context.run_migrations()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
```

Создать первую миграцию (в папке backend):

```bash
alembic revision -m "create users" --autogenerate
alembic upgrade head
```

> Примечание: если запускаете миграции внутри Docker контейнера, используйте `docker compose up --build` — команда backend в `docker-compose.yml` выполнит `alembic upgrade head` перед запуском uvicorn.

Commit alembic:

```bash
git add backend/alembic
git commit -m "sprint1: alembic init and env configured"
```

---

## 6. Backend — FastAPI приложение и healthcheck

Создать `backend/app/api/v1/routes.py`:

```python
from fastapi import APIRouter

router = APIRouter()

@router.get("/health", tags=["system"])
def healthcheck():
    return {"status": "ok"}
```

Создать `backend/app/main.py`:

```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.core.config import settings
from app.api.v1.routes import router as api_router

app = FastAPI(title=settings.PROJECT_NAME)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api_router, prefix=settings.API_V1_PREFIX)

@app.get("/", tags=["system"])
def root():
    return {"name": settings.PROJECT_NAME, "env": settings.ENVIRONMENT}
```

Commit:

```bash
git add backend/app/api backend/app/main.py
git commit -m "sprint1: add fastapi app and healthcheck"
```

---

## 7. Pre-commit конфиг и линтеры (точно)

Создать `.pre-commit-config.yaml` в корне с содержимым:

```yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-added-large-files

  - repo: https://github.com/psf/black
    rev: 24.8.0
    hooks:
      - id: black
        language_version: python3.11

  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort

  - repo: https://github.com/PyCQA/flake8
    rev: 7.1.1
    hooks:
      - id: flake8
```

Инструкция для запуска локально (в README или в docs):

```bash
pip install pre-commit
pre-commit install
pre-commit run --all-files
```

Commit:

```bash
git add .pre-commit-config.yaml
git commit -m "sprint1: add pre-commit configuration"
```

---

## 8. Тесты backend — healthcheck тест

Создать файл `backend/tests/test_health.py`:

```python
import httpx
import pytest

BASE = "http://localhost:8000"

@pytest.mark.asyncio
async def test_health():
    async with httpx.AsyncClient() as client:
        r = await client.get(f"{BASE}/api/v1/health")
        assert r.status_code == 200
        assert r.json()["status"] == "ok"
```

Commit:

```bash
git add backend/tests/test_health.py
git commit -m "sprint1: add healthcheck pytest"
```

---

## 9. Frontend — создать минимальный Flutter-каркас (точно)

Выполнить в папке `frontend`:

```bash
cd frontend
flutter create timesheet_ai
cd timesheet_ai
```

Изменить `pubspec.yaml` — добавить зависимости (в секции dependencies):

```yaml
dependencies:
  flutter:
    sdk: flutter
  dio: ^5.7.0
  intl: ^0.19.0
```

Создать файл `lib/core/http_client.dart`:

```dart
import 'package:dio/dio.dart';

class HttpClient {
  final Dio dio;
  HttpClient(String baseUrl) : dio = Dio(BaseOptions(baseUrl: baseUrl));
}
```

Создать `lib/features/system/health_repository.dart`:

```dart
import 'package:dio/dio.dart';

class HealthRepository {
  final Dio dio;
  HealthRepository(this.dio);

  Future<bool> isHealthy() async {
    final r = await dio.get('/api/v1/health');
    return r.statusCode == 200 && r.data['status'] == 'ok';
  }
}
```

Заменить `lib/main.dart` на:

```dart
import 'package:flutter/material.dart';
import 'core/http_client.dart';
import 'features/system/health_repository.dart';

void main() {
  runApp(const App());
}

class App extends StatefulWidget {
  const App({super.key});
  @override
  State<App> createState() => _AppState();
}

class _AppState extends State<App> {
  late final HealthRepository repo;
  bool? healthy;

  @override
  void initState() {
    super.initState();
    final client = HttpClient('http://********:8000'); // эмулятор Android
    repo = HealthRepository(client.dio);
    _check();
  }

  Future<void> _check() async {
    final ok = await repo.isHealthy();
    setState(() => healthy = ok);
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Timesheet AI',
      home: Scaffold(
        appBar: AppBar(title: const Text('Timesheet AI')),
        body: Center(
          child: healthy == null
              ? const CircularProgressIndicator()
              : Text(healthy! ? 'Backend OK' : 'Backend DOWN'),
        ),
      ),
    );
  }
}
```

Commit:

```bash
git add frontend/timesheet_ai
git commit -m "sprint1: add flutter skeleton with health check UI"
```

---

## 10. CI — GitHub Actions (точно)

Создать файл `.github/workflows/ci.yml` со следующим содержимым:

```yaml
name: CI

on:
  push:
    branches: [ "sprint-1", "main" ]
  pull_request:
    branches: [ "sprint-1", "main" ]

jobs:
  backend-checks:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install deps
        run: |
          python -m pip install --upgrade pip
          pip install pre-commit pytest pytest-asyncio httpx
      - name: Run pre-commit
        run: |
          pre-commit run --all-files || true
      - name: Run tests
        run: |
          pytest -q backend/tests
```

> Примечание: CI для Flutter можно добавить позже — для Sprint 1 достаточно Python checks и тестов.

Commit:

```bash
git add .github/workflows/ci.yml
git commit -m "sprint1: add CI workflow for backend checks"
```

---

## 11. Документы — минимальные файлы в `docs/`

Создать `docs/ARCHITECTURE.md` (коротко):

```
Timesheet AI — архитектура (MVP Sprint1)

Компоненты:
- backend: FastAPI (uvicorn), PostgreSQL, Alembic
- frontend: Flutter
- CI: GitHub Actions (pre-commit, pytest)
- Запуск: docker compose up --build
```

Создать `docs/SPRINTS.md` — краткий список спринтов (указать Sprint1, Sprint2 и т.д.).

Создать `docs/CONTRIBUTING.md` — скопировать правила (минимум: стиль коммитов: "sprint1: <описание>", код на русском для комментариев в будущем).

Commit:

```bash
git add docs
git commit -m "sprint1: add docs scaffolding"
```

---

## 12. Запуск и проверка локально (точно)

1. Поднять сервисы:

```bash
docker compose up --build
```

2. Ожидать логов, убедиться, что контейнеры `db` и `backend` запустились и backend слушает 0.0.0.0:8000.

3. Проверки вручную:

* Открыть в браузере `http://localhost:8000/` → ожидаемый результат:

```json
{"name": "Timesheet AI", "env": "dev"}
```

* Открыть `http://localhost:8000/docs` → Swagger UI должен быть доступен.
* Вызвать `http://localhost:8000/api/v1/health` → ожидаемый ответ:

```json
{"status":"ok"}
```

4. Запустить автотест:

```bash
# Убедитесь, что backend поднят
pytest -q backend/tests
```

Ожидаемый результат: тест проходит (exit code 0).

Если backend не отвечает — проверить логи:

```bash
docker compose logs backend --follow
docker compose logs db --follow
```

---

## 13. Коммит-мессаджи и создание задач (обязательное правило)

Для всех коммитов использовать формат:

```
sprint1: <короткое описание>
```

Создать Issues (если репозиторий на GitHub) — один issue на каждую крупную задачу:

* Issue: `sprint1/1 — Инициализация репозитория и структура`
* Issue: `sprint1/2 — Docker Compose и PostgreSQL`
* Issue: `sprint1/3 — FastAPI: config, session, модель User`
* Issue: `sprint1/4 — Alembic: инициализация и миграция`
* Issue: `sprint1/5 — Healthcheck endpoint`
* Issue: `sprint1/6 — Pre-commit и линтеры`
* Issue: `sprint1/7 — Pytest health тест`
* Issue: `sprint1/8 — Flutter skeleton`
* Issue: `sprint1/9 — CI: GitHub Actions`

Каждый issue: в описании указывать шаги для выполнения (копипаста соответствующей части этого ТЗ). Привязывать PR к issue.

---

## 14. Acceptance criteria — что принять как выполненное (Definition of Done)

Спринт 1 считается завершённым, если выполнены все пункты ниже:

1. Репозиторий с веткой `sprint-1` содержит:

   * `backend/`, `frontend/`, `docs/`, `.pre-commit-config.yaml`, `docker-compose.yml`, `.github/workflows/ci.yml`.
2. Docker Compose поднимает сервисы без ошибок: `docker compose up --build` — backend стартует.
3. Выполнена миграция Alembic — таблица `users` создана в БД.
4. `GET /api/v1/health` возвращает `{"status":"ok"}` (status 200).
5. Swagger UI доступен на `/docs`.
6. `backend/tests/test_health.py` проходит при запуске `pytest`.
7. Pre-commit успешно установлен и локально проходит для текущего кода.
8. Flutter приложение запускается (в эмуляторе) и показывает `Backend OK` при поднятом backend.
9. CI workflow запускается на PR/Push и выполняет pre-commit (или показывает ошибки) и pytest (выполняется).

---

## 15. Методы тестирования выполненного Спринта (пошагово)

### Автоматические тесты

1. Убедиться, что сервисы подняты:

```bash
docker compose up --build -d
```

2. Запустить тесты:

```bash
pip install -r backend/requirements.txt
pytest -q backend/tests
```

Ожидается: `1 passed` и статус 0.

### Ручная проверка backend

1. `curl http://localhost:8000/` → ожидаемый JSON с именем проекта.
2. `curl http://localhost:8000/api/v1/health` → `{"status":"ok"}`.
3. Откройте `http://localhost:8000/docs` — Swagger UI отображается.

### Ручная проверка frontend

1. В Android-эмуляторе: `flutter run` (в `frontend/timesheet_ai`).
2. На старте приложение делает запрос к `http://********:8000/api/v1/health` и отображает `Backend OK`.
3. Отключите backend — приложение должно переключиться и показать `Backend DOWN`.

### CI-проверка

1. Сделать push в ветку `sprint-1` или открыть PR — GitHub Actions должен запуститься.
2. Проверить лог Actions: выполняются шаги «Install deps», «Run pre-commit», «Run tests».

---

## Важные указания для ИИ-агента (жёсткие правила)

* Не добавлять новые таблицы, поля, эндпоинты или зависимости, которых нет в этом ТЗ.
* Если при выполнении автогенерации Alembic возникнут мелкие отличия (типа индекс/nullable) — не менять модель без согласования; оставь миграцию автосгенерированной.
* Все файлы и команды выполнять **точно** как в этом документе.
* Все коммиты делать с префиксом `sprint1:`.
* Если что-то не запускается — сначала смотрим логи контейнера, затем фиксируем issue с описанием ошибки и шагами воспроизведения.

